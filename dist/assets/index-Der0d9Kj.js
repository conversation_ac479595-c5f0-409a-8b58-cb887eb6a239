var om=Object.defineProperty,fm=Object.defineProperties;var dm=Object.getOwnPropertyDescriptors;var Od=Object.getOwnPropertySymbols;var hm=Object.prototype.hasOwnProperty,mm=Object.prototype.propertyIsEnumerable;var ll=(c,l)=>(l=Symbol[c])?l:Symbol.for("Symbol."+c),pm=c=>{throw TypeError(c)};var tr=(c,l,u)=>l in c?om(c,l,{enumerable:!0,configurable:!0,writable:!0,value:u}):c[l]=u,ne=(c,l)=>{for(var u in l||(l={}))hm.call(l,u)&&tr(c,u,l[u]);if(Od)for(var u of Od(l))mm.call(l,u)&&tr(c,u,l[u]);return c},ge=(c,l)=>fm(c,dm(l));var gm=(c,l)=>()=>(l||c((l={exports:{}}).exports,l),l.exports);var me=(c,l,u)=>tr(c,typeof l!="symbol"?l+"":l,u);var q=(c,l,u)=>new Promise((r,o)=>{var d=b=>{try{g(u.next(b))}catch(p){o(p)}},m=b=>{try{g(u.throw(b))}catch(p){o(p)}},g=b=>b.done?r(b.value):Promise.resolve(b.value).then(d,m);g((u=u.apply(c,l)).next())}),ln=function(c,l){this[0]=c,this[1]=l},ar=(c,l,u)=>{var r=(m,g,b,p)=>{try{var S=u[m](g),R=(g=S.value)instanceof ln,H=S.done;Promise.resolve(R?g[0]:g).then(Z=>R?r(m==="return"?m:"next",g[1]?{done:Z.done,value:Z.value}:Z,b,p):b({value:Z,done:H})).catch(Z=>r("throw",Z,b,p))}catch(Z){p(Z)}},o=m=>d[m]=g=>new Promise((b,p)=>r(m,g,b,p)),d={};return u=u.apply(c,l),d[ll("asyncIterator")]=()=>d,o("next"),o("throw"),o("return"),d},nr=c=>{var l=c[ll("asyncIterator")],u=!1,r,o={};return l==null?(l=c[ll("iterator")](),r=d=>o[d]=m=>l[d](m)):(l=l.call(c),r=d=>o[d]=m=>{if(u){if(u=!1,d==="throw")throw m;return m}return u=!0,{done:!1,value:new ln(new Promise(g=>{var b=l[d](m);b instanceof Object||pm("Object expected"),g(b)}),1)}}),o[ll("iterator")]=()=>o,r("next"),"throw"in l?r("throw"):o.throw=d=>{throw d},"return"in l&&r("return"),o},zd=(c,l,u)=>(l=c[ll("asyncIterator")])?l.call(c):(c=c[ll("iterator")](),l={},u=(r,o)=>(o=c[r])&&(l[r]=d=>new Promise((m,g,b)=>(d=o.call(c,d),b=d.done,Promise.resolve(d.value).then(p=>m({value:p,done:b}),g)))),u("next"),u("return"),l);var E3=gm(ce=>{(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const d of o)if(d.type==="childList")for(const m of d.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&r(m)}).observe(document,{childList:!0,subtree:!0});function u(o){const d={};return o.integrity&&(d.integrity=o.integrity),o.referrerPolicy&&(d.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?d.credentials="include":o.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function r(o){if(o.ep)return;o.ep=!0;const d=u(o);fetch(o.href,d)}})();function ym(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}var lr={exports:{}},ci={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ld;function vm(){if(Ld)return ci;Ld=1;var c=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function u(r,o,d){var m=null;if(d!==void 0&&(m=""+d),o.key!==void 0&&(m=""+o.key),"key"in o){d={};for(var g in o)g!=="key"&&(d[g]=o[g])}else d=o;return o=d.ref,{$$typeof:c,type:r,key:m,ref:o!==void 0?o:null,props:d}}return ci.Fragment=l,ci.jsx=u,ci.jsxs=u,ci}var Dd;function bm(){return Dd||(Dd=1,lr.exports=vm()),lr.exports}var A=bm(),ir={exports:{}},ui={},sr={exports:{}},cr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jd;function Cm(){return jd||(jd=1,function(c){function l(L,$){var le=L.length;L.push($);e:for(;0<le;){var pe=le-1>>>1,v=L[pe];if(0<o(v,$))L[pe]=$,L[le]=v,le=pe;else break e}}function u(L){return L.length===0?null:L[0]}function r(L){if(L.length===0)return null;var $=L[0],le=L.pop();if(le!==$){L[0]=le;e:for(var pe=0,v=L.length,Y=v>>>1;pe<Y;){var I=2*(pe+1)-1,F=L[I],se=I+1,Ne=L[se];if(0>o(F,le))se<v&&0>o(Ne,F)?(L[pe]=Ne,L[se]=le,pe=se):(L[pe]=F,L[I]=le,pe=I);else if(se<v&&0>o(Ne,le))L[pe]=Ne,L[se]=le,pe=se;else break e}}return $}function o(L,$){var le=L.sortIndex-$.sortIndex;return le!==0?le:L.id-$.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;c.unstable_now=function(){return d.now()}}else{var m=Date,g=m.now();c.unstable_now=function(){return m.now()-g}}var b=[],p=[],S=1,R=null,H=3,Z=!1,j=!1,V=!1,k=!1,ee=typeof setTimeout=="function"?setTimeout:null,ue=typeof clearTimeout=="function"?clearTimeout:null,P=typeof setImmediate!="undefined"?setImmediate:null;function Q(L){for(var $=u(p);$!==null;){if($.callback===null)r(p);else if($.startTime<=L)r(p),$.sortIndex=$.expirationTime,l(b,$);else break;$=u(p)}}function w(L){if(V=!1,Q(L),!j)if(u(b)!==null)j=!0,G||(G=!0,Se());else{var $=u(p);$!==null&&oe(w,$.startTime-L)}}var G=!1,O=-1,J=5,de=-1;function Le(){return k?!0:!(c.unstable_now()-de<J)}function xe(){if(k=!1,G){var L=c.unstable_now();de=L;var $=!0;try{e:{j=!1,V&&(V=!1,ue(O),O=-1),Z=!0;var le=H;try{t:{for(Q(L),R=u(b);R!==null&&!(R.expirationTime>L&&Le());){var pe=R.callback;if(typeof pe=="function"){R.callback=null,H=R.priorityLevel;var v=pe(R.expirationTime<=L);if(L=c.unstable_now(),typeof v=="function"){R.callback=v,Q(L),$=!0;break t}R===u(b)&&r(b),Q(L)}else r(b);R=u(b)}if(R!==null)$=!0;else{var Y=u(p);Y!==null&&oe(w,Y.startTime-L),$=!1}}break e}finally{R=null,H=le,Z=!1}$=void 0}}finally{$?Se():G=!1}}}var Se;if(typeof P=="function")Se=function(){P(xe)};else if(typeof MessageChannel!="undefined"){var te=new MessageChannel,re=te.port2;te.port1.onmessage=xe,Se=function(){re.postMessage(null)}}else Se=function(){ee(xe,0)};function oe(L,$){O=ee(function(){L(c.unstable_now())},$)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(L){L.callback=null},c.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):J=0<L?Math.floor(1e3/L):5},c.unstable_getCurrentPriorityLevel=function(){return H},c.unstable_next=function(L){switch(H){case 1:case 2:case 3:var $=3;break;default:$=H}var le=H;H=$;try{return L()}finally{H=le}},c.unstable_requestPaint=function(){k=!0},c.unstable_runWithPriority=function(L,$){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var le=H;H=L;try{return $()}finally{H=le}},c.unstable_scheduleCallback=function(L,$,le){var pe=c.unstable_now();switch(typeof le=="object"&&le!==null?(le=le.delay,le=typeof le=="number"&&0<le?pe+le:pe):le=pe,L){case 1:var v=-1;break;case 2:v=250;break;case 5:v=1073741823;break;case 4:v=1e4;break;default:v=5e3}return v=le+v,L={id:S++,callback:$,priorityLevel:L,startTime:le,expirationTime:v,sortIndex:-1},le>pe?(L.sortIndex=le,l(p,L),u(b)===null&&L===u(p)&&(V?(ue(O),O=-1):V=!0,oe(w,le-pe))):(L.sortIndex=v,l(b,L),j||Z||(j=!0,G||(G=!0,Se()))),L},c.unstable_shouldYield=Le,c.unstable_wrapCallback=function(L){var $=H;return function(){var le=H;H=$;try{return L.apply(this,arguments)}finally{H=le}}}}(cr)),cr}var _d;function Sm(){return _d||(_d=1,sr.exports=Cm()),sr.exports}var ur={exports:{}},Ce={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ud;function Em(){if(Ud)return Ce;Ud=1;var c=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),m=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),b=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),R=Symbol.iterator;function H(v){return v===null||typeof v!="object"?null:(v=R&&v[R]||v["@@iterator"],typeof v=="function"?v:null)}var Z={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},j=Object.assign,V={};function k(v,Y,I){this.props=v,this.context=Y,this.refs=V,this.updater=I||Z}k.prototype.isReactComponent={},k.prototype.setState=function(v,Y){if(typeof v!="object"&&typeof v!="function"&&v!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,v,Y,"setState")},k.prototype.forceUpdate=function(v){this.updater.enqueueForceUpdate(this,v,"forceUpdate")};function ee(){}ee.prototype=k.prototype;function ue(v,Y,I){this.props=v,this.context=Y,this.refs=V,this.updater=I||Z}var P=ue.prototype=new ee;P.constructor=ue,j(P,k.prototype),P.isPureReactComponent=!0;var Q=Array.isArray,w={H:null,A:null,T:null,S:null,V:null},G=Object.prototype.hasOwnProperty;function O(v,Y,I,F,se,Ne){return I=Ne.ref,{$$typeof:c,type:v,key:Y,ref:I!==void 0?I:null,props:Ne}}function J(v,Y){return O(v.type,Y,void 0,void 0,void 0,v.props)}function de(v){return typeof v=="object"&&v!==null&&v.$$typeof===c}function Le(v){var Y={"=":"=0",":":"=2"};return"$"+v.replace(/[=:]/g,function(I){return Y[I]})}var xe=/\/+/g;function Se(v,Y){return typeof v=="object"&&v!==null&&v.key!=null?Le(""+v.key):Y.toString(36)}function te(){}function re(v){switch(v.status){case"fulfilled":return v.value;case"rejected":throw v.reason;default:switch(typeof v.status=="string"?v.then(te,te):(v.status="pending",v.then(function(Y){v.status==="pending"&&(v.status="fulfilled",v.value=Y)},function(Y){v.status==="pending"&&(v.status="rejected",v.reason=Y)})),v.status){case"fulfilled":return v.value;case"rejected":throw v.reason}}throw v}function oe(v,Y,I,F,se){var Ne=typeof v;(Ne==="undefined"||Ne==="boolean")&&(v=null);var ve=!1;if(v===null)ve=!0;else switch(Ne){case"bigint":case"string":case"number":ve=!0;break;case"object":switch(v.$$typeof){case c:case l:ve=!0;break;case S:return ve=v._init,oe(ve(v._payload),Y,I,F,se)}}if(ve)return se=se(v),ve=F===""?"."+Se(v,0):F,Q(se)?(I="",ve!=null&&(I=ve.replace(xe,"$&/")+"/"),oe(se,Y,I,"",function(Ee){return Ee})):se!=null&&(de(se)&&(se=J(se,I+(se.key==null||v&&v.key===se.key?"":(""+se.key).replace(xe,"$&/")+"/")+ve)),Y.push(se)),1;ve=0;var K=F===""?".":F+":";if(Q(v))for(var W=0;W<v.length;W++)F=v[W],Ne=K+Se(F,W),ve+=oe(F,Y,I,Ne,se);else if(W=H(v),typeof W=="function")for(v=W.call(v),W=0;!(F=v.next()).done;)F=F.value,Ne=K+Se(F,W++),ve+=oe(F,Y,I,Ne,se);else if(Ne==="object"){if(typeof v.then=="function")return oe(re(v),Y,I,F,se);throw Y=String(v),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(v).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return ve}function L(v,Y,I){if(v==null)return v;var F=[],se=0;return oe(v,F,"","",function(Ne){return Y.call(I,Ne,se++)}),F}function $(v){if(v._status===-1){var Y=v._result;Y=Y(),Y.then(function(I){(v._status===0||v._status===-1)&&(v._status=1,v._result=I)},function(I){(v._status===0||v._status===-1)&&(v._status=2,v._result=I)}),v._status===-1&&(v._status=0,v._result=Y)}if(v._status===1)return v._result.default;throw v._result}var le=typeof reportError=="function"?reportError:function(v){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof v=="object"&&v!==null&&typeof v.message=="string"?String(v.message):String(v),error:v});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",v);return}console.error(v)};function pe(){}return Ce.Children={map:L,forEach:function(v,Y,I){L(v,function(){Y.apply(this,arguments)},I)},count:function(v){var Y=0;return L(v,function(){Y++}),Y},toArray:function(v){return L(v,function(Y){return Y})||[]},only:function(v){if(!de(v))throw Error("React.Children.only expected to receive a single React element child.");return v}},Ce.Component=k,Ce.Fragment=u,Ce.Profiler=o,Ce.PureComponent=ue,Ce.StrictMode=r,Ce.Suspense=b,Ce.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=w,Ce.__COMPILER_RUNTIME={__proto__:null,c:function(v){return w.H.useMemoCache(v)}},Ce.cache=function(v){return function(){return v.apply(null,arguments)}},Ce.cloneElement=function(v,Y,I){if(v==null)throw Error("The argument must be a React element, but you passed "+v+".");var F=j({},v.props),se=v.key,Ne=void 0;if(Y!=null)for(ve in Y.ref!==void 0&&(Ne=void 0),Y.key!==void 0&&(se=""+Y.key),Y)!G.call(Y,ve)||ve==="key"||ve==="__self"||ve==="__source"||ve==="ref"&&Y.ref===void 0||(F[ve]=Y[ve]);var ve=arguments.length-2;if(ve===1)F.children=I;else if(1<ve){for(var K=Array(ve),W=0;W<ve;W++)K[W]=arguments[W+2];F.children=K}return O(v.type,se,void 0,void 0,Ne,F)},Ce.createContext=function(v){return v={$$typeof:m,_currentValue:v,_currentValue2:v,_threadCount:0,Provider:null,Consumer:null},v.Provider=v,v.Consumer={$$typeof:d,_context:v},v},Ce.createElement=function(v,Y,I){var F,se={},Ne=null;if(Y!=null)for(F in Y.key!==void 0&&(Ne=""+Y.key),Y)G.call(Y,F)&&F!=="key"&&F!=="__self"&&F!=="__source"&&(se[F]=Y[F]);var ve=arguments.length-2;if(ve===1)se.children=I;else if(1<ve){for(var K=Array(ve),W=0;W<ve;W++)K[W]=arguments[W+2];se.children=K}if(v&&v.defaultProps)for(F in ve=v.defaultProps,ve)se[F]===void 0&&(se[F]=ve[F]);return O(v,Ne,void 0,void 0,null,se)},Ce.createRef=function(){return{current:null}},Ce.forwardRef=function(v){return{$$typeof:g,render:v}},Ce.isValidElement=de,Ce.lazy=function(v){return{$$typeof:S,_payload:{_status:-1,_result:v},_init:$}},Ce.memo=function(v,Y){return{$$typeof:p,type:v,compare:Y===void 0?null:Y}},Ce.startTransition=function(v){var Y=w.T,I={};w.T=I;try{var F=v(),se=w.S;se!==null&&se(I,F),typeof F=="object"&&F!==null&&typeof F.then=="function"&&F.then(pe,le)}catch(Ne){le(Ne)}finally{w.T=Y}},Ce.unstable_useCacheRefresh=function(){return w.H.useCacheRefresh()},Ce.use=function(v){return w.H.use(v)},Ce.useActionState=function(v,Y,I){return w.H.useActionState(v,Y,I)},Ce.useCallback=function(v,Y){return w.H.useCallback(v,Y)},Ce.useContext=function(v){return w.H.useContext(v)},Ce.useDebugValue=function(){},Ce.useDeferredValue=function(v,Y){return w.H.useDeferredValue(v,Y)},Ce.useEffect=function(v,Y,I){var F=w.H;if(typeof I=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return F.useEffect(v,Y)},Ce.useId=function(){return w.H.useId()},Ce.useImperativeHandle=function(v,Y,I){return w.H.useImperativeHandle(v,Y,I)},Ce.useInsertionEffect=function(v,Y){return w.H.useInsertionEffect(v,Y)},Ce.useLayoutEffect=function(v,Y){return w.H.useLayoutEffect(v,Y)},Ce.useMemo=function(v,Y){return w.H.useMemo(v,Y)},Ce.useOptimistic=function(v,Y){return w.H.useOptimistic(v,Y)},Ce.useReducer=function(v,Y,I){return w.H.useReducer(v,Y,I)},Ce.useRef=function(v){return w.H.useRef(v)},Ce.useState=function(v){return w.H.useState(v)},Ce.useSyncExternalStore=function(v,Y,I){return w.H.useSyncExternalStore(v,Y,I)},Ce.useTransition=function(){return w.H.useTransition()},Ce.version="19.1.0",Ce}var Hd;function Or(){return Hd||(Hd=1,ur.exports=Em()),ur.exports}var rr={exports:{}},dt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kd;function Am(){if(kd)return dt;kd=1;var c=Or();function l(b){var p="https://react.dev/errors/"+b;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var S=2;S<arguments.length;S++)p+="&args[]="+encodeURIComponent(arguments[S])}return"Minified React error #"+b+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var r={d:{f:u,r:function(){throw Error(l(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},o=Symbol.for("react.portal");function d(b,p,S){var R=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:R==null?null:""+R,children:b,containerInfo:p,implementation:S}}var m=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(b,p){if(b==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return dt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,dt.createPortal=function(b,p){var S=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(l(299));return d(b,p,null,S)},dt.flushSync=function(b){var p=m.T,S=r.p;try{if(m.T=null,r.p=2,b)return b()}finally{m.T=p,r.p=S,r.d.f()}},dt.preconnect=function(b,p){typeof b=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,r.d.C(b,p))},dt.prefetchDNS=function(b){typeof b=="string"&&r.d.D(b)},dt.preinit=function(b,p){if(typeof b=="string"&&p&&typeof p.as=="string"){var S=p.as,R=g(S,p.crossOrigin),H=typeof p.integrity=="string"?p.integrity:void 0,Z=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;S==="style"?r.d.S(b,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:R,integrity:H,fetchPriority:Z}):S==="script"&&r.d.X(b,{crossOrigin:R,integrity:H,fetchPriority:Z,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},dt.preinitModule=function(b,p){if(typeof b=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var S=g(p.as,p.crossOrigin);r.d.M(b,{crossOrigin:S,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&r.d.M(b)},dt.preload=function(b,p){if(typeof b=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var S=p.as,R=g(S,p.crossOrigin);r.d.L(b,S,{crossOrigin:R,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},dt.preloadModule=function(b,p){if(typeof b=="string")if(p){var S=g(p.as,p.crossOrigin);r.d.m(b,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:S,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else r.d.m(b)},dt.requestFormReset=function(b){r.d.r(b)},dt.unstable_batchedUpdates=function(b,p){return b(p)},dt.useFormState=function(b,p,S){return m.H.useFormState(b,p,S)},dt.useFormStatus=function(){return m.H.useHostTransitionStatus()},dt.version="19.1.0",dt}var qd;function Tm(){if(qd)return rr.exports;qd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(l){console.error(l)}}return c(),rr.exports=Am(),rr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bd;function wm(){if(Bd)return ui;Bd=1;var c=Sm(),l=Or(),u=Tm();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function d(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function m(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(d(e)!==e)throw Error(r(188))}function b(e){var t=e.alternate;if(!t){if(t=d(e),t===null)throw Error(r(188));return t!==e?null:e}for(var a=e,n=t;;){var i=a.return;if(i===null)break;var s=i.alternate;if(s===null){if(n=i.return,n!==null){a=n;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===a)return g(i),e;if(s===n)return g(i),t;s=s.sibling}throw Error(r(188))}if(a.return!==n.return)a=i,n=s;else{for(var f=!1,h=i.child;h;){if(h===a){f=!0,a=i,n=s;break}if(h===n){f=!0,n=i,a=s;break}h=h.sibling}if(!f){for(h=s.child;h;){if(h===a){f=!0,a=s,n=i;break}if(h===n){f=!0,n=s,a=i;break}h=h.sibling}if(!f)throw Error(r(189))}}if(a.alternate!==n)throw Error(r(190))}if(a.tag!==3)throw Error(r(188));return a.stateNode.current===a?e:t}function p(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=p(e),t!==null)return t;e=e.sibling}return null}var S=Object.assign,R=Symbol.for("react.element"),H=Symbol.for("react.transitional.element"),Z=Symbol.for("react.portal"),j=Symbol.for("react.fragment"),V=Symbol.for("react.strict_mode"),k=Symbol.for("react.profiler"),ee=Symbol.for("react.provider"),ue=Symbol.for("react.consumer"),P=Symbol.for("react.context"),Q=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),G=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),de=Symbol.for("react.activity"),Le=Symbol.for("react.memo_cache_sentinel"),xe=Symbol.iterator;function Se(e){return e===null||typeof e!="object"?null:(e=xe&&e[xe]||e["@@iterator"],typeof e=="function"?e:null)}var te=Symbol.for("react.client.reference");function re(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===te?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case j:return"Fragment";case k:return"Profiler";case V:return"StrictMode";case w:return"Suspense";case G:return"SuspenseList";case de:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Z:return"Portal";case P:return(e.displayName||"Context")+".Provider";case ue:return(e._context.displayName||"Context")+".Consumer";case Q:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case O:return t=e.displayName||null,t!==null?t:re(e.type)||"Memo";case J:t=e._payload,e=e._init;try{return re(e(t))}catch(a){}}return null}var oe=Array.isArray,L=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,le={pending:!1,data:null,method:null,action:null},pe=[],v=-1;function Y(e){return{current:e}}function I(e){0>v||(e.current=pe[v],pe[v]=null,v--)}function F(e,t){v++,pe[v]=e.current,e.current=t}var se=Y(null),Ne=Y(null),ve=Y(null),K=Y(null);function W(e,t){switch(F(ve,t),F(Ne,e),F(se,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ld(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=ld(t),e=id(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}I(se),F(se,e)}function Ee(){I(se),I(Ne),I(ve)}function We(e){e.memoizedState!==null&&F(K,e);var t=se.current,a=id(t,e.type);t!==a&&(F(Ne,e),F(se,a))}function rt(e){Ne.current===e&&(I(se),I(Ne)),K.current===e&&(I(K),ai._currentValue=le)}var rl=Object.prototype.hasOwnProperty,Gs=c.unstable_scheduleCallback,Ys=c.unstable_cancelCallback,G2=c.unstable_shouldYield,Y2=c.unstable_requestPaint,Zt=c.unstable_now,Q2=c.unstable_getCurrentPriorityLevel,Ur=c.unstable_ImmediatePriority,Hr=c.unstable_UserBlockingPriority,mi=c.unstable_NormalPriority,Z2=c.unstable_LowPriority,kr=c.unstable_IdlePriority,X2=c.log,$2=c.unstable_setDisableYieldValue,ol=null,At=null;function pa(e){if(typeof X2=="function"&&$2(e),At&&typeof At.setStrictMode=="function")try{At.setStrictMode(ol,e)}catch(t){}}var Tt=Math.clz32?Math.clz32:F2,K2=Math.log,J2=Math.LN2;function F2(e){return e>>>=0,e===0?32:31-(K2(e)/J2|0)|0}var pi=256,gi=4194304;function Ba(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function yi(e,t,a){var n=e.pendingLanes;if(n===0)return 0;var i=0,s=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var h=n&134217727;return h!==0?(n=h&~s,n!==0?i=Ba(n):(f&=h,f!==0?i=Ba(f):a||(a=h&~e,a!==0&&(i=Ba(a))))):(h=n&~s,h!==0?i=Ba(h):f!==0?i=Ba(f):a||(a=n&~e,a!==0&&(i=Ba(a)))),i===0?0:t!==0&&t!==i&&(t&s)===0&&(s=i&-i,a=t&-t,s>=a||s===32&&(a&4194048)!==0)?t:i}function fl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function W2(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function qr(){var e=pi;return pi<<=1,(pi&4194048)===0&&(pi=256),e}function Br(){var e=gi;return gi<<=1,(gi&62914560)===0&&(gi=4194304),e}function Qs(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function dl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function P2(e,t,a,n,i,s){var f=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var h=e.entanglements,y=e.expirationTimes,N=e.hiddenUpdates;for(a=f&~a;0<a;){var _=31-Tt(a),B=1<<_;h[_]=0,y[_]=-1;var x=N[_];if(x!==null)for(N[_]=null,_=0;_<x.length;_++){var z=x[_];z!==null&&(z.lane&=-536870913)}a&=~B}n!==0&&Vr(e,n,0),s!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=s&~(f&~t))}function Vr(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var n=31-Tt(t);e.entangledLanes|=t,e.entanglements[n]=e.entanglements[n]|1073741824|a&4194090}function Gr(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var n=31-Tt(a),i=1<<n;i&t|e[n]&t&&(e[n]|=t),a&=~i}}function Zs(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Xs(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Yr(){var e=$.p;return e!==0?e:(e=window.event,e===void 0?32:Td(e.type))}function I2(e,t){var a=$.p;try{return $.p=e,t()}finally{$.p=a}}var ga=Math.random().toString(36).slice(2),ot="__reactFiber$"+ga,gt="__reactProps$"+ga,bn="__reactContainer$"+ga,$s="__reactEvents$"+ga,e0="__reactListeners$"+ga,t0="__reactHandles$"+ga,Qr="__reactResources$"+ga,hl="__reactMarker$"+ga;function Ks(e){delete e[ot],delete e[gt],delete e[$s],delete e[e0],delete e[t0]}function Cn(e){var t=e[ot];if(t)return t;for(var a=e.parentNode;a;){if(t=a[bn]||a[ot]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=rd(e);e!==null;){if(a=e[ot])return a;e=rd(e)}return t}e=a,a=e.parentNode}return null}function Sn(e){if(e=e[ot]||e[bn]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ml(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(r(33))}function En(e){var t=e[Qr];return t||(t=e[Qr]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function et(e){e[hl]=!0}var Zr=new Set,Xr={};function Va(e,t){An(e,t),An(e+"Capture",t)}function An(e,t){for(Xr[e]=t,e=0;e<t.length;e++)Zr.add(t[e])}var a0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),$r={},Kr={};function n0(e){return rl.call(Kr,e)?!0:rl.call($r,e)?!1:a0.test(e)?Kr[e]=!0:($r[e]=!0,!1)}function vi(e,t,a){if(n0(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var n=t.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function bi(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function It(e,t,a,n){if(n===null)e.removeAttribute(a);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+n)}}var Js,Jr;function Tn(e){if(Js===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);Js=t&&t[1]||"",Jr=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Js+e+Jr}var Fs=!1;function Ws(e,t){if(!e||Fs)return"";Fs=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(t){var B=function(){throw Error()};if(Object.defineProperty(B.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(B,[])}catch(z){var x=z}Reflect.construct(e,[],B)}else{try{B.call()}catch(z){x=z}e.call(B.prototype)}}else{try{throw Error()}catch(z){x=z}(B=e())&&typeof B.catch=="function"&&B.catch(function(){})}}catch(z){if(z&&x&&typeof z.stack=="string")return[z.stack,x.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=n.DetermineComponentFrameRoot(),f=s[0],h=s[1];if(f&&h){var y=f.split(`
`),N=h.split(`
`);for(i=n=0;n<y.length&&!y[n].includes("DetermineComponentFrameRoot");)n++;for(;i<N.length&&!N[i].includes("DetermineComponentFrameRoot");)i++;if(n===y.length||i===N.length)for(n=y.length-1,i=N.length-1;1<=n&&0<=i&&y[n]!==N[i];)i--;for(;1<=n&&0<=i;n--,i--)if(y[n]!==N[i]){if(n!==1||i!==1)do if(n--,i--,0>i||y[n]!==N[i]){var _=`
`+y[n].replace(" at new "," at ");return e.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",e.displayName)),_}while(1<=n&&0<=i);break}}}finally{Fs=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?Tn(a):""}function l0(e){switch(e.tag){case 26:case 27:case 5:return Tn(e.type);case 16:return Tn("Lazy");case 13:return Tn("Suspense");case 19:return Tn("SuspenseList");case 0:case 15:return Ws(e.type,!1);case 11:return Ws(e.type.render,!1);case 1:return Ws(e.type,!0);case 31:return Tn("Activity");default:return""}}function Fr(e){try{var t="";do t+=l0(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Lt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Wr(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function i0(e){var t=Wr(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof a!="undefined"&&typeof a.get=="function"&&typeof a.set=="function"){var i=a.get,s=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(f){n=""+f,s.call(this,f)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return n},setValue:function(f){n=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ci(e){e._valueTracker||(e._valueTracker=i0(e))}function Pr(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),n="";return e&&(n=Wr(e)?e.checked?"true":"false":e.value),e=n,e!==a?(t.setValue(e),!0):!1}function Si(e){if(e=e||(typeof document!="undefined"?document:void 0),typeof e=="undefined")return null;try{return e.activeElement||e.body}catch(t){return e.body}}var s0=/[\n"\\]/g;function Dt(e){return e.replace(s0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Ps(e,t,a,n,i,s,f,h){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Lt(t)):e.value!==""+Lt(t)&&(e.value=""+Lt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?Is(e,f,Lt(t)):a!=null?Is(e,f,Lt(a)):n!=null&&e.removeAttribute("value"),i==null&&s!=null&&(e.defaultChecked=!!s),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.name=""+Lt(h):e.removeAttribute("name")}function Ir(e,t,a,n,i,s,f,h){if(s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(e.type=s),t!=null||a!=null){if(!(s!=="submit"&&s!=="reset"||t!=null))return;a=a!=null?""+Lt(a):"",t=t!=null?""+Lt(t):a,h||t===e.value||(e.value=t),e.defaultValue=t}n=n!=null?n:i,n=typeof n!="function"&&typeof n!="symbol"&&!!n,e.checked=h?e.checked:!!n,e.defaultChecked=!!n,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function Is(e,t,a){t==="number"&&Si(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function wn(e,t,a,n){if(e=e.options,t){t={};for(var i=0;i<a.length;i++)t["$"+a[i]]=!0;for(a=0;a<e.length;a++)i=t.hasOwnProperty("$"+e[a].value),e[a].selected!==i&&(e[a].selected=i),i&&n&&(e[a].defaultSelected=!0)}else{for(a=""+Lt(a),t=null,i=0;i<e.length;i++){if(e[i].value===a){e[i].selected=!0,n&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function eo(e,t,a){if(t!=null&&(t=""+Lt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Lt(a):""}function to(e,t,a,n){if(t==null){if(n!=null){if(a!=null)throw Error(r(92));if(oe(n)){if(1<n.length)throw Error(r(93));n=n[0]}a=n}a==null&&(a=""),t=a}a=Lt(t),e.defaultValue=a,n=e.textContent,n===a&&n!==""&&n!==null&&(e.value=n)}function Mn(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var c0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ao(e,t,a){var n=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?n?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":n?e.setProperty(t,a):typeof a!="number"||a===0||c0.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function no(e,t,a){if(t!=null&&typeof t!="object")throw Error(r(62));if(e=e.style,a!=null){for(var n in a)!a.hasOwnProperty(n)||t!=null&&t.hasOwnProperty(n)||(n.indexOf("--")===0?e.setProperty(n,""):n==="float"?e.cssFloat="":e[n]="");for(var i in t)n=t[i],t.hasOwnProperty(i)&&a[i]!==n&&ao(e,i,n)}else for(var s in t)t.hasOwnProperty(s)&&ao(e,s,t[s])}function ec(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var u0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),r0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ei(e){return r0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var tc=null;function ac(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Nn=null,Rn=null;function lo(e){var t=Sn(e);if(t&&(e=t.stateNode)){var a=e[gt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Ps(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Dt(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var n=a[t];if(n!==e&&n.form===e.form){var i=n[gt]||null;if(!i)throw Error(r(90));Ps(n,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<a.length;t++)n=a[t],n.form===e.form&&Pr(n)}break e;case"textarea":eo(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&wn(e,!!a.multiple,t,!1)}}}var nc=!1;function io(e,t,a){if(nc)return e(t,a);nc=!0;try{var n=e(t);return n}finally{if(nc=!1,(Nn!==null||Rn!==null)&&(cs(),Nn&&(t=Nn,e=Rn,Rn=Nn=null,lo(t),e)))for(t=0;t<e.length;t++)lo(e[t])}}function pl(e,t){var a=e.stateNode;if(a===null)return null;var n=a[gt]||null;if(n===null)return null;a=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(r(231,t,typeof a));return a}var ea=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),lc=!1;if(ea)try{var gl={};Object.defineProperty(gl,"passive",{get:function(){lc=!0}}),window.addEventListener("test",gl,gl),window.removeEventListener("test",gl,gl)}catch(e){lc=!1}var ya=null,ic=null,Ai=null;function so(){if(Ai)return Ai;var e,t=ic,a=t.length,n,i="value"in ya?ya.value:ya.textContent,s=i.length;for(e=0;e<a&&t[e]===i[e];e++);var f=a-e;for(n=1;n<=f&&t[a-n]===i[s-n];n++);return Ai=i.slice(e,1<n?1-n:void 0)}function Ti(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function wi(){return!0}function co(){return!1}function yt(e){function t(a,n,i,s,f){this._reactName=a,this._targetInst=i,this.type=n,this.nativeEvent=s,this.target=f,this.currentTarget=null;for(var h in e)e.hasOwnProperty(h)&&(a=e[h],this[h]=a?a(s):s[h]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?wi:co,this.isPropagationStopped=co,this}return S(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=wi)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=wi)},persist:function(){},isPersistent:wi}),t}var Ga={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Mi=yt(Ga),yl=S({},Ga,{view:0,detail:0}),o0=yt(yl),sc,cc,vl,Ni=S({},yl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:rc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==vl&&(vl&&e.type==="mousemove"?(sc=e.screenX-vl.screenX,cc=e.screenY-vl.screenY):cc=sc=0,vl=e),sc)},movementY:function(e){return"movementY"in e?e.movementY:cc}}),uo=yt(Ni),f0=S({},Ni,{dataTransfer:0}),d0=yt(f0),h0=S({},yl,{relatedTarget:0}),uc=yt(h0),m0=S({},Ga,{animationName:0,elapsedTime:0,pseudoElement:0}),p0=yt(m0),g0=S({},Ga,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),y0=yt(g0),v0=S({},Ga,{data:0}),ro=yt(v0),b0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},C0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},S0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function E0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=S0[e])?!!t[e]:!1}function rc(){return E0}var A0=S({},yl,{key:function(e){if(e.key){var t=b0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ti(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?C0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:rc,charCode:function(e){return e.type==="keypress"?Ti(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ti(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),T0=yt(A0),w0=S({},Ni,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),oo=yt(w0),M0=S({},yl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:rc}),N0=yt(M0),R0=S({},Ga,{propertyName:0,elapsedTime:0,pseudoElement:0}),x0=yt(R0),O0=S({},Ni,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),z0=yt(O0),L0=S({},Ga,{newState:0,oldState:0}),D0=yt(L0),j0=[9,13,27,32],oc=ea&&"CompositionEvent"in window,bl=null;ea&&"documentMode"in document&&(bl=document.documentMode);var _0=ea&&"TextEvent"in window&&!bl,fo=ea&&(!oc||bl&&8<bl&&11>=bl),ho=" ",mo=!1;function po(e,t){switch(e){case"keyup":return j0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function go(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var xn=!1;function U0(e,t){switch(e){case"compositionend":return go(t);case"keypress":return t.which!==32?null:(mo=!0,ho);case"textInput":return e=t.data,e===ho&&mo?null:e;default:return null}}function H0(e,t){if(xn)return e==="compositionend"||!oc&&po(e,t)?(e=so(),Ai=ic=ya=null,xn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return fo&&t.locale!=="ko"?null:t.data;default:return null}}var k0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!k0[e.type]:t==="textarea"}function vo(e,t,a,n){Nn?Rn?Rn.push(n):Rn=[n]:Nn=n,t=hs(t,"onChange"),0<t.length&&(a=new Mi("onChange","change",null,a,n),e.push({event:a,listeners:t}))}var Cl=null,Sl=null;function q0(e){I1(e,0)}function Ri(e){var t=ml(e);if(Pr(t))return e}function bo(e,t){if(e==="change")return t}var Co=!1;if(ea){var fc;if(ea){var dc="oninput"in document;if(!dc){var So=document.createElement("div");So.setAttribute("oninput","return;"),dc=typeof So.oninput=="function"}fc=dc}else fc=!1;Co=fc&&(!document.documentMode||9<document.documentMode)}function Eo(){Cl&&(Cl.detachEvent("onpropertychange",Ao),Sl=Cl=null)}function Ao(e){if(e.propertyName==="value"&&Ri(Sl)){var t=[];vo(t,Sl,e,ac(e)),io(q0,t)}}function B0(e,t,a){e==="focusin"?(Eo(),Cl=t,Sl=a,Cl.attachEvent("onpropertychange",Ao)):e==="focusout"&&Eo()}function V0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ri(Sl)}function G0(e,t){if(e==="click")return Ri(t)}function Y0(e,t){if(e==="input"||e==="change")return Ri(t)}function Q0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var wt=typeof Object.is=="function"?Object.is:Q0;function El(e,t){if(wt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),n=Object.keys(t);if(a.length!==n.length)return!1;for(n=0;n<a.length;n++){var i=a[n];if(!rl.call(t,i)||!wt(e[i],t[i]))return!1}return!0}function To(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wo(e,t){var a=To(e);e=0;for(var n;a;){if(a.nodeType===3){if(n=e+a.textContent.length,e<=t&&n>=t)return{node:a,offset:t-e};e=n}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=To(a)}}function Mo(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Mo(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function No(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Si(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch(n){a=!1}if(a)e=t.contentWindow;else break;t=Si(e.document)}return t}function hc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Z0=ea&&"documentMode"in document&&11>=document.documentMode,On=null,mc=null,Al=null,pc=!1;function Ro(e,t,a){var n=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;pc||On==null||On!==Si(n)||(n=On,"selectionStart"in n&&hc(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Al&&El(Al,n)||(Al=n,n=hs(mc,"onSelect"),0<n.length&&(t=new Mi("onSelect","select",null,t,a),e.push({event:t,listeners:n}),t.target=On)))}function Ya(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var zn={animationend:Ya("Animation","AnimationEnd"),animationiteration:Ya("Animation","AnimationIteration"),animationstart:Ya("Animation","AnimationStart"),transitionrun:Ya("Transition","TransitionRun"),transitionstart:Ya("Transition","TransitionStart"),transitioncancel:Ya("Transition","TransitionCancel"),transitionend:Ya("Transition","TransitionEnd")},gc={},xo={};ea&&(xo=document.createElement("div").style,"AnimationEvent"in window||(delete zn.animationend.animation,delete zn.animationiteration.animation,delete zn.animationstart.animation),"TransitionEvent"in window||delete zn.transitionend.transition);function Qa(e){if(gc[e])return gc[e];if(!zn[e])return e;var t=zn[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in xo)return gc[e]=t[a];return e}var Oo=Qa("animationend"),zo=Qa("animationiteration"),Lo=Qa("animationstart"),X0=Qa("transitionrun"),$0=Qa("transitionstart"),K0=Qa("transitioncancel"),Do=Qa("transitionend"),jo=new Map,yc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");yc.push("scrollEnd");function Vt(e,t){jo.set(e,t),Va(t,[e])}var _o=new WeakMap;function jt(e,t){if(typeof e=="object"&&e!==null){var a=_o.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Fr(t)},_o.set(e,t),t)}return{value:e,source:t,stack:Fr(t)}}var _t=[],Ln=0,vc=0;function xi(){for(var e=Ln,t=vc=Ln=0;t<e;){var a=_t[t];_t[t++]=null;var n=_t[t];_t[t++]=null;var i=_t[t];_t[t++]=null;var s=_t[t];if(_t[t++]=null,n!==null&&i!==null){var f=n.pending;f===null?i.next=i:(i.next=f.next,f.next=i),n.pending=i}s!==0&&Uo(a,i,s)}}function Oi(e,t,a,n){_t[Ln++]=e,_t[Ln++]=t,_t[Ln++]=a,_t[Ln++]=n,vc|=n,e.lanes|=n,e=e.alternate,e!==null&&(e.lanes|=n)}function bc(e,t,a,n){return Oi(e,t,a,n),zi(e)}function Dn(e,t){return Oi(e,null,null,t),zi(e)}function Uo(e,t,a){e.lanes|=a;var n=e.alternate;n!==null&&(n.lanes|=a);for(var i=!1,s=e.return;s!==null;)s.childLanes|=a,n=s.alternate,n!==null&&(n.childLanes|=a),s.tag===22&&(e=s.stateNode,e===null||e._visibility&1||(i=!0)),e=s,s=s.return;return e.tag===3?(s=e.stateNode,i&&t!==null&&(i=31-Tt(a),e=s.hiddenUpdates,n=e[i],n===null?e[i]=[t]:n.push(t),t.lane=a|536870912),s):null}function zi(e){if(50<Kl)throw Kl=0,wu=null,Error(r(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var jn={};function J0(e,t,a,n){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Mt(e,t,a,n){return new J0(e,t,a,n)}function Cc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ta(e,t){var a=e.alternate;return a===null?(a=Mt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Ho(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Li(e,t,a,n,i,s){var f=0;if(n=e,typeof e=="function")Cc(e)&&(f=1);else if(typeof e=="string")f=Wh(e,a,se.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case de:return e=Mt(31,a,t,i),e.elementType=de,e.lanes=s,e;case j:return Za(a.children,i,s,t);case V:f=8,i|=24;break;case k:return e=Mt(12,a,t,i|2),e.elementType=k,e.lanes=s,e;case w:return e=Mt(13,a,t,i),e.elementType=w,e.lanes=s,e;case G:return e=Mt(19,a,t,i),e.elementType=G,e.lanes=s,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ee:case P:f=10;break e;case ue:f=9;break e;case Q:f=11;break e;case O:f=14;break e;case J:f=16,n=null;break e}f=29,a=Error(r(130,e===null?"null":typeof e,"")),n=null}return t=Mt(f,a,t,i),t.elementType=e,t.type=n,t.lanes=s,t}function Za(e,t,a,n){return e=Mt(7,e,n,t),e.lanes=a,e}function Sc(e,t,a){return e=Mt(6,e,null,t),e.lanes=a,e}function Ec(e,t,a){return t=Mt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var _n=[],Un=0,Di=null,ji=0,Ut=[],Ht=0,Xa=null,aa=1,na="";function $a(e,t){_n[Un++]=ji,_n[Un++]=Di,Di=e,ji=t}function ko(e,t,a){Ut[Ht++]=aa,Ut[Ht++]=na,Ut[Ht++]=Xa,Xa=e;var n=aa;e=na;var i=32-Tt(n)-1;n&=~(1<<i),a+=1;var s=32-Tt(t)+i;if(30<s){var f=i-i%5;s=(n&(1<<f)-1).toString(32),n>>=f,i-=f,aa=1<<32-Tt(t)+i|a<<i|n,na=s+e}else aa=1<<s|a<<i|n,na=e}function Ac(e){e.return!==null&&($a(e,1),ko(e,1,0))}function Tc(e){for(;e===Di;)Di=_n[--Un],_n[Un]=null,ji=_n[--Un],_n[Un]=null;for(;e===Xa;)Xa=Ut[--Ht],Ut[Ht]=null,na=Ut[--Ht],Ut[Ht]=null,aa=Ut[--Ht],Ut[Ht]=null}var mt=null,Qe=null,ze=!1,Ka=null,Xt=!1,wc=Error(r(519));function Ja(e){var t=Error(r(418,""));throw Ml(jt(t,e)),wc}function qo(e){var t=e.stateNode,a=e.type,n=e.memoizedProps;switch(t[ot]=e,t[gt]=n,a){case"dialog":Me("cancel",t),Me("close",t);break;case"iframe":case"object":case"embed":Me("load",t);break;case"video":case"audio":for(a=0;a<Fl.length;a++)Me(Fl[a],t);break;case"source":Me("error",t);break;case"img":case"image":case"link":Me("error",t),Me("load",t);break;case"details":Me("toggle",t);break;case"input":Me("invalid",t),Ir(t,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),Ci(t);break;case"select":Me("invalid",t);break;case"textarea":Me("invalid",t),to(t,n.value,n.defaultValue,n.children),Ci(t)}a=n.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||n.suppressHydrationWarning===!0||nd(t.textContent,a)?(n.popover!=null&&(Me("beforetoggle",t),Me("toggle",t)),n.onScroll!=null&&Me("scroll",t),n.onScrollEnd!=null&&Me("scrollend",t),n.onClick!=null&&(t.onclick=ms),t=!0):t=!1,t||Ja(e)}function Bo(e){for(mt=e.return;mt;)switch(mt.tag){case 5:case 13:Xt=!1;return;case 27:case 3:Xt=!0;return;default:mt=mt.return}}function Tl(e){if(e!==mt)return!1;if(!ze)return Bo(e),ze=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Vu(e.type,e.memoizedProps)),a=!a),a&&Qe&&Ja(e),Bo(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Qe=Yt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Qe=null}}else t===27?(t=Qe,Da(e.type)?(e=Zu,Zu=null,Qe=e):Qe=t):Qe=mt?Yt(e.stateNode.nextSibling):null;return!0}function wl(){Qe=mt=null,ze=!1}function Vo(){var e=Ka;return e!==null&&(Ct===null?Ct=e:Ct.push.apply(Ct,e),Ka=null),e}function Ml(e){Ka===null?Ka=[e]:Ka.push(e)}var Mc=Y(null),Fa=null,la=null;function va(e,t,a){F(Mc,t._currentValue),t._currentValue=a}function ia(e){e._currentValue=Mc.current,I(Mc)}function Nc(e,t,a){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===a)break;e=e.return}}function Rc(e,t,a,n){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var s=i.dependencies;if(s!==null){var f=i.child;s=s.firstContext;e:for(;s!==null;){var h=s;s=i;for(var y=0;y<t.length;y++)if(h.context===t[y]){s.lanes|=a,h=s.alternate,h!==null&&(h.lanes|=a),Nc(s.return,a,e),n||(f=null);break e}s=h.next}}else if(i.tag===18){if(f=i.return,f===null)throw Error(r(341));f.lanes|=a,s=f.alternate,s!==null&&(s.lanes|=a),Nc(f,a,e),f=null}else f=i.child;if(f!==null)f.return=i;else for(f=i;f!==null;){if(f===e){f=null;break}if(i=f.sibling,i!==null){i.return=f.return,f=i;break}f=f.return}i=f}}function Nl(e,t,a,n){e=null;for(var i=t,s=!1;i!==null;){if(!s){if((i.flags&524288)!==0)s=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var f=i.alternate;if(f===null)throw Error(r(387));if(f=f.memoizedProps,f!==null){var h=i.type;wt(i.pendingProps.value,f.value)||(e!==null?e.push(h):e=[h])}}else if(i===K.current){if(f=i.alternate,f===null)throw Error(r(387));f.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(ai):e=[ai])}i=i.return}e!==null&&Rc(t,e,a,n),t.flags|=262144}function _i(e){for(e=e.firstContext;e!==null;){if(!wt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Wa(e){Fa=e,la=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ft(e){return Go(Fa,e)}function Ui(e,t){return Fa===null&&Wa(e),Go(e,t)}function Go(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},la===null){if(e===null)throw Error(r(308));la=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else la=la.next=t;return a}var F0=typeof AbortController!="undefined"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},W0=c.unstable_scheduleCallback,P0=c.unstable_NormalPriority,Pe={$$typeof:P,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function xc(){return{controller:new F0,data:new Map,refCount:0}}function Rl(e){e.refCount--,e.refCount===0&&W0(P0,function(){e.controller.abort()})}var xl=null,Oc=0,Hn=0,kn=null;function I0(e,t){if(xl===null){var a=xl=[];Oc=0,Hn=Lu(),kn={status:"pending",value:void 0,then:function(n){a.push(n)}}}return Oc++,t.then(Yo,Yo),t}function Yo(){if(--Oc===0&&xl!==null){kn!==null&&(kn.status="fulfilled");var e=xl;xl=null,Hn=0,kn=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function eh(e,t){var a=[],n={status:"pending",value:null,reason:null,then:function(i){a.push(i)}};return e.then(function(){n.status="fulfilled",n.value=t;for(var i=0;i<a.length;i++)(0,a[i])(t)},function(i){for(n.status="rejected",n.reason=i,i=0;i<a.length;i++)(0,a[i])(void 0)}),n}var Qo=L.S;L.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&I0(e,t),Qo!==null&&Qo(e,t)};var Pa=Y(null);function zc(){var e=Pa.current;return e!==null?e:qe.pooledCache}function Hi(e,t){t===null?F(Pa,Pa.current):F(Pa,t.pool)}function Zo(){var e=zc();return e===null?null:{parent:Pe._currentValue,pool:e}}var Ol=Error(r(460)),Xo=Error(r(474)),ki=Error(r(542)),Lc={then:function(){}};function $o(e){return e=e.status,e==="fulfilled"||e==="rejected"}function qi(){}function Ko(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(qi,qi),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Fo(e),e;default:if(typeof t.status=="string")t.then(qi,qi);else{if(e=qe,e!==null&&100<e.shellSuspendCounter)throw Error(r(482));e=t,e.status="pending",e.then(function(n){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=n}},function(n){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=n}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Fo(e),e}throw zl=t,Ol}}var zl=null;function Jo(){if(zl===null)throw Error(r(459));var e=zl;return zl=null,e}function Fo(e){if(e===Ol||e===ki)throw Error(r(483))}var ba=!1;function Dc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function jc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Ca(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Sa(e,t,a){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,(De&2)!==0){var i=n.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),n.pending=t,t=zi(e),Uo(e,null,a),t}return Oi(e,n,t,a),zi(e)}function Ll(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var n=t.lanes;n&=e.pendingLanes,a|=n,t.lanes=a,Gr(e,a)}}function _c(e,t){var a=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,a===n)){var i=null,s=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};s===null?i=s=f:s=s.next=f,a=a.next}while(a!==null);s===null?i=s=t:s=s.next=t}else i=s=t;a={baseState:n.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:n.shared,callbacks:n.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Uc=!1;function Dl(){if(Uc){var e=kn;if(e!==null)throw e}}function jl(e,t,a,n){Uc=!1;var i=e.updateQueue;ba=!1;var s=i.firstBaseUpdate,f=i.lastBaseUpdate,h=i.shared.pending;if(h!==null){i.shared.pending=null;var y=h,N=y.next;y.next=null,f===null?s=N:f.next=N,f=y;var _=e.alternate;_!==null&&(_=_.updateQueue,h=_.lastBaseUpdate,h!==f&&(h===null?_.firstBaseUpdate=N:h.next=N,_.lastBaseUpdate=y))}if(s!==null){var B=i.baseState;f=0,_=N=y=null,h=s;do{var x=h.lane&-536870913,z=x!==h.lane;if(z?(Re&x)===x:(n&x)===x){x!==0&&x===Hn&&(Uc=!0),_!==null&&(_=_.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});e:{var ye=e,fe=h;x=t;var He=a;switch(fe.tag){case 1:if(ye=fe.payload,typeof ye=="function"){B=ye.call(He,B,x);break e}B=ye;break e;case 3:ye.flags=ye.flags&-65537|128;case 0:if(ye=fe.payload,x=typeof ye=="function"?ye.call(He,B,x):ye,x==null)break e;B=S({},B,x);break e;case 2:ba=!0}}x=h.callback,x!==null&&(e.flags|=64,z&&(e.flags|=8192),z=i.callbacks,z===null?i.callbacks=[x]:z.push(x))}else z={lane:x,tag:h.tag,payload:h.payload,callback:h.callback,next:null},_===null?(N=_=z,y=B):_=_.next=z,f|=x;if(h=h.next,h===null){if(h=i.shared.pending,h===null)break;z=h,h=z.next,z.next=null,i.lastBaseUpdate=z,i.shared.pending=null}}while(!0);_===null&&(y=B),i.baseState=y,i.firstBaseUpdate=N,i.lastBaseUpdate=_,s===null&&(i.shared.lanes=0),xa|=f,e.lanes=f,e.memoizedState=B}}function Wo(e,t){if(typeof e!="function")throw Error(r(191,e));e.call(t)}function Po(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Wo(a[e],t)}var qn=Y(null),Bi=Y(0);function Io(e,t){e=da,F(Bi,e),F(qn,t),da=e|t.baseLanes}function Hc(){F(Bi,da),F(qn,qn.current)}function kc(){da=Bi.current,I(qn),I(Bi)}var Ea=0,Ae=null,_e=null,Ke=null,Vi=!1,Bn=!1,Ia=!1,Gi=0,_l=0,Vn=null,th=0;function Xe(){throw Error(r(321))}function qc(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!wt(e[a],t[a]))return!1;return!0}function Bc(e,t,a,n,i,s){return Ea=s,Ae=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,L.H=e===null||e.memoizedState===null?Hf:kf,Ia=!1,s=a(n,i),Ia=!1,Bn&&(s=tf(t,a,n,i)),ef(e),s}function ef(e){L.H=Ki;var t=_e!==null&&_e.next!==null;if(Ea=0,Ke=_e=Ae=null,Vi=!1,_l=0,Vn=null,t)throw Error(r(300));e===null||tt||(e=e.dependencies,e!==null&&_i(e)&&(tt=!0))}function tf(e,t,a,n){Ae=e;var i=0;do{if(Bn&&(Vn=null),_l=0,Bn=!1,25<=i)throw Error(r(301));if(i+=1,Ke=_e=null,e.updateQueue!=null){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,s.memoCache!=null&&(s.memoCache.index=0)}L.H=uh,s=t(a,n)}while(Bn);return s}function ah(){var e=L.H,t=e.useState()[0];return t=typeof t.then=="function"?Ul(t):t,e=e.useState()[0],(_e!==null?_e.memoizedState:null)!==e&&(Ae.flags|=1024),t}function Vc(){var e=Gi!==0;return Gi=0,e}function Gc(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Yc(e){if(Vi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Vi=!1}Ea=0,Ke=_e=Ae=null,Bn=!1,_l=Gi=0,Vn=null}function vt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ke===null?Ae.memoizedState=Ke=e:Ke=Ke.next=e,Ke}function Je(){if(_e===null){var e=Ae.alternate;e=e!==null?e.memoizedState:null}else e=_e.next;var t=Ke===null?Ae.memoizedState:Ke.next;if(t!==null)Ke=t,_e=e;else{if(e===null)throw Ae.alternate===null?Error(r(467)):Error(r(310));_e=e,e={memoizedState:_e.memoizedState,baseState:_e.baseState,baseQueue:_e.baseQueue,queue:_e.queue,next:null},Ke===null?Ae.memoizedState=Ke=e:Ke=Ke.next=e}return Ke}function Qc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ul(e){var t=_l;return _l+=1,Vn===null&&(Vn=[]),e=Ko(Vn,e,t),t=Ae,(Ke===null?t.memoizedState:Ke.next)===null&&(t=t.alternate,L.H=t===null||t.memoizedState===null?Hf:kf),e}function Yi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ul(e);if(e.$$typeof===P)return ft(e)}throw Error(r(438,String(e)))}function Zc(e){var t=null,a=Ae.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var n=Ae.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(t={data:n.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Qc(),Ae.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),n=0;n<e;n++)a[n]=Le;return t.index++,a}function sa(e,t){return typeof t=="function"?t(e):t}function Qi(e){var t=Je();return Xc(t,_e,e)}function Xc(e,t,a){var n=e.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=a;var i=e.baseQueue,s=n.pending;if(s!==null){if(i!==null){var f=i.next;i.next=s.next,s.next=f}t.baseQueue=i=s,n.pending=null}if(s=e.baseState,i===null)e.memoizedState=s;else{t=i.next;var h=f=null,y=null,N=t,_=!1;do{var B=N.lane&-536870913;if(B!==N.lane?(Re&B)===B:(Ea&B)===B){var x=N.revertLane;if(x===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null}),B===Hn&&(_=!0);else if((Ea&x)===x){N=N.next,x===Hn&&(_=!0);continue}else B={lane:0,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},y===null?(h=y=B,f=s):y=y.next=B,Ae.lanes|=x,xa|=x;B=N.action,Ia&&a(s,B),s=N.hasEagerState?N.eagerState:a(s,B)}else x={lane:B,revertLane:N.revertLane,action:N.action,hasEagerState:N.hasEagerState,eagerState:N.eagerState,next:null},y===null?(h=y=x,f=s):y=y.next=x,Ae.lanes|=B,xa|=B;N=N.next}while(N!==null&&N!==t);if(y===null?f=s:y.next=h,!wt(s,e.memoizedState)&&(tt=!0,_&&(a=kn,a!==null)))throw a;e.memoizedState=s,e.baseState=f,e.baseQueue=y,n.lastRenderedState=s}return i===null&&(n.lanes=0),[e.memoizedState,n.dispatch]}function $c(e){var t=Je(),a=t.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=e;var n=a.dispatch,i=a.pending,s=t.memoizedState;if(i!==null){a.pending=null;var f=i=i.next;do s=e(s,f.action),f=f.next;while(f!==i);wt(s,t.memoizedState)||(tt=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),a.lastRenderedState=s}return[s,n]}function af(e,t,a){var n=Ae,i=Je(),s=ze;if(s){if(a===void 0)throw Error(r(407));a=a()}else a=t();var f=!wt((_e||i).memoizedState,a);f&&(i.memoizedState=a,tt=!0),i=i.queue;var h=sf.bind(null,n,i,e);if(Hl(2048,8,h,[e]),i.getSnapshot!==t||f||Ke!==null&&Ke.memoizedState.tag&1){if(n.flags|=2048,Gn(9,Zi(),lf.bind(null,n,i,a,t),null),qe===null)throw Error(r(349));s||(Ea&124)!==0||nf(n,t,a)}return a}function nf(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=Ae.updateQueue,t===null?(t=Qc(),Ae.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function lf(e,t,a,n){t.value=a,t.getSnapshot=n,cf(t)&&uf(e)}function sf(e,t,a){return a(function(){cf(t)&&uf(e)})}function cf(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!wt(e,a)}catch(n){return!0}}function uf(e){var t=Dn(e,2);t!==null&&zt(t,e,2)}function Kc(e){var t=vt();if(typeof e=="function"){var a=e;if(e=a(),Ia){pa(!0);try{a()}finally{pa(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:sa,lastRenderedState:e},t}function rf(e,t,a,n){return e.baseState=a,Xc(e,_e,typeof n=="function"?n:sa)}function nh(e,t,a,n,i){if($i(e))throw Error(r(485));if(e=t.action,e!==null){var s={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){s.listeners.push(f)}};L.T!==null?a(!0):s.isTransition=!1,n(s),a=t.pending,a===null?(s.next=t.pending=s,of(t,s)):(s.next=a.next,t.pending=a.next=s)}}function of(e,t){var a=t.action,n=t.payload,i=e.state;if(t.isTransition){var s=L.T,f={};L.T=f;try{var h=a(i,n),y=L.S;y!==null&&y(f,h),ff(e,t,h)}catch(N){Jc(e,t,N)}finally{L.T=s}}else try{s=a(i,n),ff(e,t,s)}catch(N){Jc(e,t,N)}}function ff(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(n){df(e,t,n)},function(n){return Jc(e,t,n)}):df(e,t,a)}function df(e,t,a){t.status="fulfilled",t.value=a,hf(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,of(e,a)))}function Jc(e,t,a){var n=e.pending;if(e.pending=null,n!==null){n=n.next;do t.status="rejected",t.reason=a,hf(t),t=t.next;while(t!==n)}e.action=null}function hf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function mf(e,t){return t}function pf(e,t){if(ze){var a=qe.formState;if(a!==null){e:{var n=Ae;if(ze){if(Qe){t:{for(var i=Qe,s=Xt;i.nodeType!==8;){if(!s){i=null;break t}if(i=Yt(i.nextSibling),i===null){i=null;break t}}s=i.data,i=s==="F!"||s==="F"?i:null}if(i){Qe=Yt(i.nextSibling),n=i.data==="F!";break e}}Ja(n)}n=!1}n&&(t=a[0])}}return a=vt(),a.memoizedState=a.baseState=t,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:mf,lastRenderedState:t},a.queue=n,a=jf.bind(null,Ae,n),n.dispatch=a,n=Kc(!1),s=eu.bind(null,Ae,!1,n.queue),n=vt(),i={state:t,dispatch:null,action:e,pending:null},n.queue=i,a=nh.bind(null,Ae,i,s,a),i.dispatch=a,n.memoizedState=e,[t,a,!1]}function gf(e){var t=Je();return yf(t,_e,e)}function yf(e,t,a){if(t=Xc(e,t,mf)[0],e=Qi(sa)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var n=Ul(t)}catch(f){throw f===Ol?ki:f}else n=t;t=Je();var i=t.queue,s=i.dispatch;return a!==t.memoizedState&&(Ae.flags|=2048,Gn(9,Zi(),lh.bind(null,i,a),null)),[n,s,e]}function lh(e,t){e.action=t}function vf(e){var t=Je(),a=_e;if(a!==null)return yf(t,a,e);Je(),t=t.memoizedState,a=Je();var n=a.queue.dispatch;return a.memoizedState=e,[t,n,!1]}function Gn(e,t,a,n){return e={tag:e,create:a,deps:n,inst:t,next:null},t=Ae.updateQueue,t===null&&(t=Qc(),Ae.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(n=a.next,a.next=e,e.next=n,t.lastEffect=e),e}function Zi(){return{destroy:void 0,resource:void 0}}function bf(){return Je().memoizedState}function Xi(e,t,a,n){var i=vt();n=n===void 0?null:n,Ae.flags|=e,i.memoizedState=Gn(1|t,Zi(),a,n)}function Hl(e,t,a,n){var i=Je();n=n===void 0?null:n;var s=i.memoizedState.inst;_e!==null&&n!==null&&qc(n,_e.memoizedState.deps)?i.memoizedState=Gn(t,s,a,n):(Ae.flags|=e,i.memoizedState=Gn(1|t,s,a,n))}function Cf(e,t){Xi(8390656,8,e,t)}function Sf(e,t){Hl(2048,8,e,t)}function Ef(e,t){return Hl(4,2,e,t)}function Af(e,t){return Hl(4,4,e,t)}function Tf(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function wf(e,t,a){a=a!=null?a.concat([e]):null,Hl(4,4,Tf.bind(null,t,e),a)}function Fc(){}function Mf(e,t){var a=Je();t=t===void 0?null:t;var n=a.memoizedState;return t!==null&&qc(t,n[1])?n[0]:(a.memoizedState=[e,t],e)}function Nf(e,t){var a=Je();t=t===void 0?null:t;var n=a.memoizedState;if(t!==null&&qc(t,n[1]))return n[0];if(n=e(),Ia){pa(!0);try{e()}finally{pa(!1)}}return a.memoizedState=[n,t],n}function Wc(e,t,a){return a===void 0||(Ea&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=O1(),Ae.lanes|=e,xa|=e,a)}function Rf(e,t,a,n){return wt(a,t)?a:qn.current!==null?(e=Wc(e,a,n),wt(e,t)||(tt=!0),e):(Ea&42)===0?(tt=!0,e.memoizedState=a):(e=O1(),Ae.lanes|=e,xa|=e,t)}function xf(e,t,a,n,i){var s=$.p;$.p=s!==0&&8>s?s:8;var f=L.T,h={};L.T=h,eu(e,!1,t,a);try{var y=i(),N=L.S;if(N!==null&&N(h,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var _=eh(y,n);kl(e,t,_,Ot(e))}else kl(e,t,n,Ot(e))}catch(B){kl(e,t,{then:function(){},status:"rejected",reason:B},Ot())}finally{$.p=s,L.T=f}}function ih(){}function Pc(e,t,a,n){if(e.tag!==5)throw Error(r(476));var i=Of(e).queue;xf(e,i,t,le,a===null?ih:function(){return zf(e),a(n)})}function Of(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:le,baseState:le,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sa,lastRenderedState:le},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sa,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function zf(e){var t=Of(e).next.queue;kl(e,t,{},Ot())}function Ic(){return ft(ai)}function Lf(){return Je().memoizedState}function Df(){return Je().memoizedState}function sh(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=Ot();e=Ca(a);var n=Sa(t,e,a);n!==null&&(zt(n,t,a),Ll(n,t,a)),t={cache:xc()},e.payload=t;return}t=t.return}}function ch(e,t,a){var n=Ot();a={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},$i(e)?_f(t,a):(a=bc(e,t,a,n),a!==null&&(zt(a,e,n),Uf(a,t,n)))}function jf(e,t,a){var n=Ot();kl(e,t,a,n)}function kl(e,t,a,n){var i={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if($i(e))_f(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var f=t.lastRenderedState,h=s(f,a);if(i.hasEagerState=!0,i.eagerState=h,wt(h,f))return Oi(e,t,i,0),qe===null&&xi(),!1}catch(y){}finally{}if(a=bc(e,t,i,n),a!==null)return zt(a,e,n),Uf(a,t,n),!0}return!1}function eu(e,t,a,n){if(n={lane:2,revertLane:Lu(),action:n,hasEagerState:!1,eagerState:null,next:null},$i(e)){if(t)throw Error(r(479))}else t=bc(e,a,n,2),t!==null&&zt(t,e,2)}function $i(e){var t=e.alternate;return e===Ae||t!==null&&t===Ae}function _f(e,t){Bn=Vi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Uf(e,t,a){if((a&4194048)!==0){var n=t.lanes;n&=e.pendingLanes,a|=n,t.lanes=a,Gr(e,a)}}var Ki={readContext:ft,use:Yi,useCallback:Xe,useContext:Xe,useEffect:Xe,useImperativeHandle:Xe,useLayoutEffect:Xe,useInsertionEffect:Xe,useMemo:Xe,useReducer:Xe,useRef:Xe,useState:Xe,useDebugValue:Xe,useDeferredValue:Xe,useTransition:Xe,useSyncExternalStore:Xe,useId:Xe,useHostTransitionStatus:Xe,useFormState:Xe,useActionState:Xe,useOptimistic:Xe,useMemoCache:Xe,useCacheRefresh:Xe},Hf={readContext:ft,use:Yi,useCallback:function(e,t){return vt().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:Cf,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Xi(4194308,4,Tf.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Xi(4194308,4,e,t)},useInsertionEffect:function(e,t){Xi(4,2,e,t)},useMemo:function(e,t){var a=vt();t=t===void 0?null:t;var n=e();if(Ia){pa(!0);try{e()}finally{pa(!1)}}return a.memoizedState=[n,t],n},useReducer:function(e,t,a){var n=vt();if(a!==void 0){var i=a(t);if(Ia){pa(!0);try{a(t)}finally{pa(!1)}}}else i=t;return n.memoizedState=n.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},n.queue=e,e=e.dispatch=ch.bind(null,Ae,e),[n.memoizedState,e]},useRef:function(e){var t=vt();return e={current:e},t.memoizedState=e},useState:function(e){e=Kc(e);var t=e.queue,a=jf.bind(null,Ae,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Fc,useDeferredValue:function(e,t){var a=vt();return Wc(a,e,t)},useTransition:function(){var e=Kc(!1);return e=xf.bind(null,Ae,e.queue,!0,!1),vt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var n=Ae,i=vt();if(ze){if(a===void 0)throw Error(r(407));a=a()}else{if(a=t(),qe===null)throw Error(r(349));(Re&124)!==0||nf(n,t,a)}i.memoizedState=a;var s={value:a,getSnapshot:t};return i.queue=s,Cf(sf.bind(null,n,s,e),[e]),n.flags|=2048,Gn(9,Zi(),lf.bind(null,n,s,a,t),null),a},useId:function(){var e=vt(),t=qe.identifierPrefix;if(ze){var a=na,n=aa;a=(n&~(1<<32-Tt(n)-1)).toString(32)+a,t="«"+t+"R"+a,a=Gi++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=th++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ic,useFormState:pf,useActionState:pf,useOptimistic:function(e){var t=vt();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=eu.bind(null,Ae,!0,a),a.dispatch=t,[e,t]},useMemoCache:Zc,useCacheRefresh:function(){return vt().memoizedState=sh.bind(null,Ae)}},kf={readContext:ft,use:Yi,useCallback:Mf,useContext:ft,useEffect:Sf,useImperativeHandle:wf,useInsertionEffect:Ef,useLayoutEffect:Af,useMemo:Nf,useReducer:Qi,useRef:bf,useState:function(){return Qi(sa)},useDebugValue:Fc,useDeferredValue:function(e,t){var a=Je();return Rf(a,_e.memoizedState,e,t)},useTransition:function(){var e=Qi(sa)[0],t=Je().memoizedState;return[typeof e=="boolean"?e:Ul(e),t]},useSyncExternalStore:af,useId:Lf,useHostTransitionStatus:Ic,useFormState:gf,useActionState:gf,useOptimistic:function(e,t){var a=Je();return rf(a,_e,e,t)},useMemoCache:Zc,useCacheRefresh:Df},uh={readContext:ft,use:Yi,useCallback:Mf,useContext:ft,useEffect:Sf,useImperativeHandle:wf,useInsertionEffect:Ef,useLayoutEffect:Af,useMemo:Nf,useReducer:$c,useRef:bf,useState:function(){return $c(sa)},useDebugValue:Fc,useDeferredValue:function(e,t){var a=Je();return _e===null?Wc(a,e,t):Rf(a,_e.memoizedState,e,t)},useTransition:function(){var e=$c(sa)[0],t=Je().memoizedState;return[typeof e=="boolean"?e:Ul(e),t]},useSyncExternalStore:af,useId:Lf,useHostTransitionStatus:Ic,useFormState:vf,useActionState:vf,useOptimistic:function(e,t){var a=Je();return _e!==null?rf(a,_e,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Zc,useCacheRefresh:Df},Yn=null,ql=0;function Ji(e){var t=ql;return ql+=1,Yn===null&&(Yn=[]),Ko(Yn,e,t)}function Bl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Fi(e,t){throw t.$$typeof===R?Error(r(525)):(e=Object.prototype.toString.call(t),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function qf(e){var t=e._init;return t(e._payload)}function Bf(e){function t(T,E){if(e){var M=T.deletions;M===null?(T.deletions=[E],T.flags|=16):M.push(E)}}function a(T,E){if(!e)return null;for(;E!==null;)t(T,E),E=E.sibling;return null}function n(T){for(var E=new Map;T!==null;)T.key!==null?E.set(T.key,T):E.set(T.index,T),T=T.sibling;return E}function i(T,E){return T=ta(T,E),T.index=0,T.sibling=null,T}function s(T,E,M){return T.index=M,e?(M=T.alternate,M!==null?(M=M.index,M<E?(T.flags|=67108866,E):M):(T.flags|=67108866,E)):(T.flags|=1048576,E)}function f(T){return e&&T.alternate===null&&(T.flags|=67108866),T}function h(T,E,M,U){return E===null||E.tag!==6?(E=Sc(M,T.mode,U),E.return=T,E):(E=i(E,M),E.return=T,E)}function y(T,E,M,U){var ae=M.type;return ae===j?_(T,E,M.props.children,U,M.key):E!==null&&(E.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===J&&qf(ae)===E.type)?(E=i(E,M.props),Bl(E,M),E.return=T,E):(E=Li(M.type,M.key,M.props,null,T.mode,U),Bl(E,M),E.return=T,E)}function N(T,E,M,U){return E===null||E.tag!==4||E.stateNode.containerInfo!==M.containerInfo||E.stateNode.implementation!==M.implementation?(E=Ec(M,T.mode,U),E.return=T,E):(E=i(E,M.children||[]),E.return=T,E)}function _(T,E,M,U,ae){return E===null||E.tag!==7?(E=Za(M,T.mode,U,ae),E.return=T,E):(E=i(E,M),E.return=T,E)}function B(T,E,M){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=Sc(""+E,T.mode,M),E.return=T,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case H:return M=Li(E.type,E.key,E.props,null,T.mode,M),Bl(M,E),M.return=T,M;case Z:return E=Ec(E,T.mode,M),E.return=T,E;case J:var U=E._init;return E=U(E._payload),B(T,E,M)}if(oe(E)||Se(E))return E=Za(E,T.mode,M,null),E.return=T,E;if(typeof E.then=="function")return B(T,Ji(E),M);if(E.$$typeof===P)return B(T,Ui(T,E),M);Fi(T,E)}return null}function x(T,E,M,U){var ae=E!==null?E.key:null;if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return ae!==null?null:h(T,E,""+M,U);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case H:return M.key===ae?y(T,E,M,U):null;case Z:return M.key===ae?N(T,E,M,U):null;case J:return ae=M._init,M=ae(M._payload),x(T,E,M,U)}if(oe(M)||Se(M))return ae!==null?null:_(T,E,M,U,null);if(typeof M.then=="function")return x(T,E,Ji(M),U);if(M.$$typeof===P)return x(T,E,Ui(T,M),U);Fi(T,M)}return null}function z(T,E,M,U,ae){if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return T=T.get(M)||null,h(E,T,""+U,ae);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case H:return T=T.get(U.key===null?M:U.key)||null,y(E,T,U,ae);case Z:return T=T.get(U.key===null?M:U.key)||null,N(E,T,U,ae);case J:var Te=U._init;return U=Te(U._payload),z(T,E,M,U,ae)}if(oe(U)||Se(U))return T=T.get(M)||null,_(E,T,U,ae,null);if(typeof U.then=="function")return z(T,E,M,Ji(U),ae);if(U.$$typeof===P)return z(T,E,M,Ui(E,U),ae);Fi(E,U)}return null}function ye(T,E,M,U){for(var ae=null,Te=null,ie=E,he=E=0,nt=null;ie!==null&&he<M.length;he++){ie.index>he?(nt=ie,ie=null):nt=ie.sibling;var Oe=x(T,ie,M[he],U);if(Oe===null){ie===null&&(ie=nt);break}e&&ie&&Oe.alternate===null&&t(T,ie),E=s(Oe,E,he),Te===null?ae=Oe:Te.sibling=Oe,Te=Oe,ie=nt}if(he===M.length)return a(T,ie),ze&&$a(T,he),ae;if(ie===null){for(;he<M.length;he++)ie=B(T,M[he],U),ie!==null&&(E=s(ie,E,he),Te===null?ae=ie:Te.sibling=ie,Te=ie);return ze&&$a(T,he),ae}for(ie=n(ie);he<M.length;he++)nt=z(ie,T,he,M[he],U),nt!==null&&(e&&nt.alternate!==null&&ie.delete(nt.key===null?he:nt.key),E=s(nt,E,he),Te===null?ae=nt:Te.sibling=nt,Te=nt);return e&&ie.forEach(function(ka){return t(T,ka)}),ze&&$a(T,he),ae}function fe(T,E,M,U){if(M==null)throw Error(r(151));for(var ae=null,Te=null,ie=E,he=E=0,nt=null,Oe=M.next();ie!==null&&!Oe.done;he++,Oe=M.next()){ie.index>he?(nt=ie,ie=null):nt=ie.sibling;var ka=x(T,ie,Oe.value,U);if(ka===null){ie===null&&(ie=nt);break}e&&ie&&ka.alternate===null&&t(T,ie),E=s(ka,E,he),Te===null?ae=ka:Te.sibling=ka,Te=ka,ie=nt}if(Oe.done)return a(T,ie),ze&&$a(T,he),ae;if(ie===null){for(;!Oe.done;he++,Oe=M.next())Oe=B(T,Oe.value,U),Oe!==null&&(E=s(Oe,E,he),Te===null?ae=Oe:Te.sibling=Oe,Te=Oe);return ze&&$a(T,he),ae}for(ie=n(ie);!Oe.done;he++,Oe=M.next())Oe=z(ie,T,he,Oe.value,U),Oe!==null&&(e&&Oe.alternate!==null&&ie.delete(Oe.key===null?he:Oe.key),E=s(Oe,E,he),Te===null?ae=Oe:Te.sibling=Oe,Te=Oe);return e&&ie.forEach(function(rm){return t(T,rm)}),ze&&$a(T,he),ae}function He(T,E,M,U){if(typeof M=="object"&&M!==null&&M.type===j&&M.key===null&&(M=M.props.children),typeof M=="object"&&M!==null){switch(M.$$typeof){case H:e:{for(var ae=M.key;E!==null;){if(E.key===ae){if(ae=M.type,ae===j){if(E.tag===7){a(T,E.sibling),U=i(E,M.props.children),U.return=T,T=U;break e}}else if(E.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===J&&qf(ae)===E.type){a(T,E.sibling),U=i(E,M.props),Bl(U,M),U.return=T,T=U;break e}a(T,E);break}else t(T,E);E=E.sibling}M.type===j?(U=Za(M.props.children,T.mode,U,M.key),U.return=T,T=U):(U=Li(M.type,M.key,M.props,null,T.mode,U),Bl(U,M),U.return=T,T=U)}return f(T);case Z:e:{for(ae=M.key;E!==null;){if(E.key===ae)if(E.tag===4&&E.stateNode.containerInfo===M.containerInfo&&E.stateNode.implementation===M.implementation){a(T,E.sibling),U=i(E,M.children||[]),U.return=T,T=U;break e}else{a(T,E);break}else t(T,E);E=E.sibling}U=Ec(M,T.mode,U),U.return=T,T=U}return f(T);case J:return ae=M._init,M=ae(M._payload),He(T,E,M,U)}if(oe(M))return ye(T,E,M,U);if(Se(M)){if(ae=Se(M),typeof ae!="function")throw Error(r(150));return M=ae.call(M),fe(T,E,M,U)}if(typeof M.then=="function")return He(T,E,Ji(M),U);if(M.$$typeof===P)return He(T,E,Ui(T,M),U);Fi(T,M)}return typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint"?(M=""+M,E!==null&&E.tag===6?(a(T,E.sibling),U=i(E,M),U.return=T,T=U):(a(T,E),U=Sc(M,T.mode,U),U.return=T,T=U),f(T)):a(T,E)}return function(T,E,M,U){try{ql=0;var ae=He(T,E,M,U);return Yn=null,ae}catch(ie){if(ie===Ol||ie===ki)throw ie;var Te=Mt(29,ie,null,T.mode);return Te.lanes=U,Te.return=T,Te}finally{}}}var Qn=Bf(!0),Vf=Bf(!1),kt=Y(null),$t=null;function Aa(e){var t=e.alternate;F(Ie,Ie.current&1),F(kt,e),$t===null&&(t===null||qn.current!==null||t.memoizedState!==null)&&($t=e)}function Gf(e){if(e.tag===22){if(F(Ie,Ie.current),F(kt,e),$t===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&($t=e)}}else Ta()}function Ta(){F(Ie,Ie.current),F(kt,kt.current)}function ca(e){I(kt),$t===e&&($t=null),I(Ie)}var Ie=Y(0);function Wi(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Qu(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function tu(e,t,a,n){t=e.memoizedState,a=a(n,t),a=a==null?t:S({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var au={enqueueSetState:function(e,t,a){e=e._reactInternals;var n=Ot(),i=Ca(n);i.payload=t,a!=null&&(i.callback=a),t=Sa(e,i,n),t!==null&&(zt(t,e,n),Ll(t,e,n))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var n=Ot(),i=Ca(n);i.tag=1,i.payload=t,a!=null&&(i.callback=a),t=Sa(e,i,n),t!==null&&(zt(t,e,n),Ll(t,e,n))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=Ot(),n=Ca(a);n.tag=2,t!=null&&(n.callback=t),t=Sa(e,n,a),t!==null&&(zt(t,e,a),Ll(t,e,a))}};function Yf(e,t,a,n,i,s,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,s,f):t.prototype&&t.prototype.isPureReactComponent?!El(a,n)||!El(i,s):!0}function Qf(e,t,a,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,n),t.state!==e&&au.enqueueReplaceState(t,t.state,null)}function en(e,t){var a=t;if("ref"in t){a={};for(var n in t)n!=="ref"&&(a[n]=t[n])}if(e=e.defaultProps){a===t&&(a=S({},a));for(var i in e)a[i]===void 0&&(a[i]=e[i])}return a}var Pi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Zf(e){Pi(e)}function Xf(e){console.error(e)}function $f(e){Pi(e)}function Ii(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function Kf(e,t,a){try{var n=e.onCaughtError;n(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function nu(e,t,a){return a=Ca(a),a.tag=3,a.payload={element:null},a.callback=function(){Ii(e,t)},a}function Jf(e){return e=Ca(e),e.tag=3,e}function Ff(e,t,a,n){var i=a.type.getDerivedStateFromError;if(typeof i=="function"){var s=n.value;e.payload=function(){return i(s)},e.callback=function(){Kf(t,a,n)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){Kf(t,a,n),typeof i!="function"&&(Oa===null?Oa=new Set([this]):Oa.add(this));var h=n.stack;this.componentDidCatch(n.value,{componentStack:h!==null?h:""})})}function rh(e,t,a,n,i){if(a.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(t=a.alternate,t!==null&&Nl(t,a,i,!0),a=kt.current,a!==null){switch(a.tag){case 13:return $t===null?Nu():a.alternate===null&&Ze===0&&(Ze=3),a.flags&=-257,a.flags|=65536,a.lanes=i,n===Lc?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([n]):t.add(n),xu(e,n,i)),!1;case 22:return a.flags|=65536,n===Lc?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([n])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([n]):a.add(n)),xu(e,n,i)),!1}throw Error(r(435,a.tag))}return xu(e,n,i),Nu(),!1}if(ze)return t=kt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,n!==wc&&(e=Error(r(422),{cause:n}),Ml(jt(e,a)))):(n!==wc&&(t=Error(r(423),{cause:n}),Ml(jt(t,a))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,n=jt(n,a),i=nu(e.stateNode,n,i),_c(e,i),Ze!==4&&(Ze=2)),!1;var s=Error(r(520),{cause:n});if(s=jt(s,a),$l===null?$l=[s]:$l.push(s),Ze!==4&&(Ze=2),t===null)return!0;n=jt(n,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=i&-i,a.lanes|=e,e=nu(a.stateNode,n,e),_c(a,e),!1;case 1:if(t=a.type,s=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||s!==null&&typeof s.componentDidCatch=="function"&&(Oa===null||!Oa.has(s))))return a.flags|=65536,i&=-i,a.lanes|=i,i=Jf(i),Ff(i,e,a,n),_c(a,i),!1}a=a.return}while(a!==null);return!1}var Wf=Error(r(461)),tt=!1;function lt(e,t,a,n){t.child=e===null?Vf(t,null,a,n):Qn(t,e.child,a,n)}function Pf(e,t,a,n,i){a=a.render;var s=t.ref;if("ref"in n){var f={};for(var h in n)h!=="ref"&&(f[h]=n[h])}else f=n;return Wa(t),n=Bc(e,t,a,f,s,i),h=Vc(),e!==null&&!tt?(Gc(e,t,i),ua(e,t,i)):(ze&&h&&Ac(t),t.flags|=1,lt(e,t,n,i),t.child)}function If(e,t,a,n,i){if(e===null){var s=a.type;return typeof s=="function"&&!Cc(s)&&s.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=s,e1(e,t,s,n,i)):(e=Li(a.type,null,n,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!fu(e,i)){var f=s.memoizedProps;if(a=a.compare,a=a!==null?a:El,a(f,n)&&e.ref===t.ref)return ua(e,t,i)}return t.flags|=1,e=ta(s,n),e.ref=t.ref,e.return=t,t.child=e}function e1(e,t,a,n,i){if(e!==null){var s=e.memoizedProps;if(El(s,n)&&e.ref===t.ref)if(tt=!1,t.pendingProps=n=s,fu(e,i))(e.flags&131072)!==0&&(tt=!0);else return t.lanes=e.lanes,ua(e,t,i)}return lu(e,t,a,n,i)}function t1(e,t,a){var n=t.pendingProps,i=n.children,s=e!==null?e.memoizedState:null;if(n.mode==="hidden"){if((t.flags&128)!==0){if(n=s!==null?s.baseLanes|a:a,e!==null){for(i=t.child=e.child,s=0;i!==null;)s=s|i.lanes|i.childLanes,i=i.sibling;t.childLanes=s&~n}else t.childLanes=0,t.child=null;return a1(e,t,n,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Hi(t,s!==null?s.cachePool:null),s!==null?Io(t,s):Hc(),Gf(t);else return t.lanes=t.childLanes=536870912,a1(e,t,s!==null?s.baseLanes|a:a,a)}else s!==null?(Hi(t,s.cachePool),Io(t,s),Ta(),t.memoizedState=null):(e!==null&&Hi(t,null),Hc(),Ta());return lt(e,t,i,a),t.child}function a1(e,t,a,n){var i=zc();return i=i===null?null:{parent:Pe._currentValue,pool:i},t.memoizedState={baseLanes:a,cachePool:i},e!==null&&Hi(t,null),Hc(),Gf(t),e!==null&&Nl(e,t,n,!0),null}function es(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(r(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function lu(e,t,a,n,i){return Wa(t),a=Bc(e,t,a,n,void 0,i),n=Vc(),e!==null&&!tt?(Gc(e,t,i),ua(e,t,i)):(ze&&n&&Ac(t),t.flags|=1,lt(e,t,a,i),t.child)}function n1(e,t,a,n,i,s){return Wa(t),t.updateQueue=null,a=tf(t,n,a,i),ef(e),n=Vc(),e!==null&&!tt?(Gc(e,t,s),ua(e,t,s)):(ze&&n&&Ac(t),t.flags|=1,lt(e,t,a,s),t.child)}function l1(e,t,a,n,i){if(Wa(t),t.stateNode===null){var s=jn,f=a.contextType;typeof f=="object"&&f!==null&&(s=ft(f)),s=new a(n,s),t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,s.updater=au,t.stateNode=s,s._reactInternals=t,s=t.stateNode,s.props=n,s.state=t.memoizedState,s.refs={},Dc(t),f=a.contextType,s.context=typeof f=="object"&&f!==null?ft(f):jn,s.state=t.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(tu(t,a,f,n),s.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(f=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),f!==s.state&&au.enqueueReplaceState(s,s.state,null),jl(t,n,s,i),Dl(),s.state=t.memoizedState),typeof s.componentDidMount=="function"&&(t.flags|=4194308),n=!0}else if(e===null){s=t.stateNode;var h=t.memoizedProps,y=en(a,h);s.props=y;var N=s.context,_=a.contextType;f=jn,typeof _=="object"&&_!==null&&(f=ft(_));var B=a.getDerivedStateFromProps;_=typeof B=="function"||typeof s.getSnapshotBeforeUpdate=="function",h=t.pendingProps!==h,_||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(h||N!==f)&&Qf(t,s,n,f),ba=!1;var x=t.memoizedState;s.state=x,jl(t,n,s,i),Dl(),N=t.memoizedState,h||x!==N||ba?(typeof B=="function"&&(tu(t,a,B,n),N=t.memoizedState),(y=ba||Yf(t,a,y,n,x,N,f))?(_||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=N),s.props=n,s.state=N,s.context=f,n=y):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{s=t.stateNode,jc(e,t),f=t.memoizedProps,_=en(a,f),s.props=_,B=t.pendingProps,x=s.context,N=a.contextType,y=jn,typeof N=="object"&&N!==null&&(y=ft(N)),h=a.getDerivedStateFromProps,(N=typeof h=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(f!==B||x!==y)&&Qf(t,s,n,y),ba=!1,x=t.memoizedState,s.state=x,jl(t,n,s,i),Dl();var z=t.memoizedState;f!==B||x!==z||ba||e!==null&&e.dependencies!==null&&_i(e.dependencies)?(typeof h=="function"&&(tu(t,a,h,n),z=t.memoizedState),(_=ba||Yf(t,a,_,n,x,z,y)||e!==null&&e.dependencies!==null&&_i(e.dependencies))?(N||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(n,z,y),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(n,z,y)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||f===e.memoizedProps&&x===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&x===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=z),s.props=n,s.state=z,s.context=y,n=_):(typeof s.componentDidUpdate!="function"||f===e.memoizedProps&&x===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&x===e.memoizedState||(t.flags|=1024),n=!1)}return s=n,es(e,t),n=(t.flags&128)!==0,s||n?(s=t.stateNode,a=n&&typeof a.getDerivedStateFromError!="function"?null:s.render(),t.flags|=1,e!==null&&n?(t.child=Qn(t,e.child,null,i),t.child=Qn(t,null,a,i)):lt(e,t,a,i),t.memoizedState=s.state,e=t.child):e=ua(e,t,i),e}function i1(e,t,a,n){return wl(),t.flags|=256,lt(e,t,a,n),t.child}var iu={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function su(e){return{baseLanes:e,cachePool:Zo()}}function cu(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=qt),e}function s1(e,t,a){var n=t.pendingProps,i=!1,s=(t.flags&128)!==0,f;if((f=s)||(f=e!==null&&e.memoizedState===null?!1:(Ie.current&2)!==0),f&&(i=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(ze){if(i?Aa(t):Ta(),ze){var h=Qe,y;if(y=h){e:{for(y=h,h=Xt;y.nodeType!==8;){if(!h){h=null;break e}if(y=Yt(y.nextSibling),y===null){h=null;break e}}h=y}h!==null?(t.memoizedState={dehydrated:h,treeContext:Xa!==null?{id:aa,overflow:na}:null,retryLane:536870912,hydrationErrors:null},y=Mt(18,null,null,0),y.stateNode=h,y.return=t,t.child=y,mt=t,Qe=null,y=!0):y=!1}y||Ja(t)}if(h=t.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return Qu(h)?t.lanes=32:t.lanes=536870912,null;ca(t)}return h=n.children,n=n.fallback,i?(Ta(),i=t.mode,h=ts({mode:"hidden",children:h},i),n=Za(n,i,a,null),h.return=t,n.return=t,h.sibling=n,t.child=h,i=t.child,i.memoizedState=su(a),i.childLanes=cu(e,f,a),t.memoizedState=iu,n):(Aa(t),uu(t,h))}if(y=e.memoizedState,y!==null&&(h=y.dehydrated,h!==null)){if(s)t.flags&256?(Aa(t),t.flags&=-257,t=ru(e,t,a)):t.memoizedState!==null?(Ta(),t.child=e.child,t.flags|=128,t=null):(Ta(),i=n.fallback,h=t.mode,n=ts({mode:"visible",children:n.children},h),i=Za(i,h,a,null),i.flags|=2,n.return=t,i.return=t,n.sibling=i,t.child=n,Qn(t,e.child,null,a),n=t.child,n.memoizedState=su(a),n.childLanes=cu(e,f,a),t.memoizedState=iu,t=i);else if(Aa(t),Qu(h)){if(f=h.nextSibling&&h.nextSibling.dataset,f)var N=f.dgst;f=N,n=Error(r(419)),n.stack="",n.digest=f,Ml({value:n,source:null,stack:null}),t=ru(e,t,a)}else if(tt||Nl(e,t,a,!1),f=(a&e.childLanes)!==0,tt||f){if(f=qe,f!==null&&(n=a&-a,n=(n&42)!==0?1:Zs(n),n=(n&(f.suspendedLanes|a))!==0?0:n,n!==0&&n!==y.retryLane))throw y.retryLane=n,Dn(e,n),zt(f,e,n),Wf;h.data==="$?"||Nu(),t=ru(e,t,a)}else h.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=y.treeContext,Qe=Yt(h.nextSibling),mt=t,ze=!0,Ka=null,Xt=!1,e!==null&&(Ut[Ht++]=aa,Ut[Ht++]=na,Ut[Ht++]=Xa,aa=e.id,na=e.overflow,Xa=t),t=uu(t,n.children),t.flags|=4096);return t}return i?(Ta(),i=n.fallback,h=t.mode,y=e.child,N=y.sibling,n=ta(y,{mode:"hidden",children:n.children}),n.subtreeFlags=y.subtreeFlags&65011712,N!==null?i=ta(N,i):(i=Za(i,h,a,null),i.flags|=2),i.return=t,n.return=t,n.sibling=i,t.child=n,n=i,i=t.child,h=e.child.memoizedState,h===null?h=su(a):(y=h.cachePool,y!==null?(N=Pe._currentValue,y=y.parent!==N?{parent:N,pool:N}:y):y=Zo(),h={baseLanes:h.baseLanes|a,cachePool:y}),i.memoizedState=h,i.childLanes=cu(e,f,a),t.memoizedState=iu,n):(Aa(t),a=e.child,e=a.sibling,a=ta(a,{mode:"visible",children:n.children}),a.return=t,a.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=a,t.memoizedState=null,a)}function uu(e,t){return t=ts({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ts(e,t){return e=Mt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function ru(e,t,a){return Qn(t,e.child,null,a),e=uu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function c1(e,t,a){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Nc(e.return,t,a)}function ou(e,t,a,n,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:a,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=n,s.tail=a,s.tailMode=i)}function u1(e,t,a){var n=t.pendingProps,i=n.revealOrder,s=n.tail;if(lt(e,t,n.children,a),n=Ie.current,(n&2)!==0)n=n&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&c1(e,a,t);else if(e.tag===19)c1(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}switch(F(Ie,n),i){case"forwards":for(a=t.child,i=null;a!==null;)e=a.alternate,e!==null&&Wi(e)===null&&(i=a),a=a.sibling;a=i,a===null?(i=t.child,t.child=null):(i=a.sibling,a.sibling=null),ou(t,!1,i,a,s);break;case"backwards":for(a=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Wi(e)===null){t.child=i;break}e=i.sibling,i.sibling=a,a=i,i=e}ou(t,!0,a,null,s);break;case"together":ou(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ua(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),xa|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(Nl(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(r(153));if(t.child!==null){for(e=t.child,a=ta(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=ta(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function fu(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&_i(e)))}function oh(e,t,a){switch(t.tag){case 3:W(t,t.stateNode.containerInfo),va(t,Pe,e.memoizedState.cache),wl();break;case 27:case 5:We(t);break;case 4:W(t,t.stateNode.containerInfo);break;case 10:va(t,t.type,t.memoizedProps.value);break;case 13:var n=t.memoizedState;if(n!==null)return n.dehydrated!==null?(Aa(t),t.flags|=128,null):(a&t.child.childLanes)!==0?s1(e,t,a):(Aa(t),e=ua(e,t,a),e!==null?e.sibling:null);Aa(t);break;case 19:var i=(e.flags&128)!==0;if(n=(a&t.childLanes)!==0,n||(Nl(e,t,a,!1),n=(a&t.childLanes)!==0),i){if(n)return u1(e,t,a);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),F(Ie,Ie.current),n)break;return null;case 22:case 23:return t.lanes=0,t1(e,t,a);case 24:va(t,Pe,e.memoizedState.cache)}return ua(e,t,a)}function r1(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)tt=!0;else{if(!fu(e,a)&&(t.flags&128)===0)return tt=!1,oh(e,t,a);tt=(e.flags&131072)!==0}else tt=!1,ze&&(t.flags&1048576)!==0&&ko(t,ji,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var n=t.elementType,i=n._init;if(n=i(n._payload),t.type=n,typeof n=="function")Cc(n)?(e=en(n,e),t.tag=1,t=l1(null,t,n,e,a)):(t.tag=0,t=lu(null,t,n,e,a));else{if(n!=null){if(i=n.$$typeof,i===Q){t.tag=11,t=Pf(null,t,n,e,a);break e}else if(i===O){t.tag=14,t=If(null,t,n,e,a);break e}}throw t=re(n)||n,Error(r(306,t,""))}}return t;case 0:return lu(e,t,t.type,t.pendingProps,a);case 1:return n=t.type,i=en(n,t.pendingProps),l1(e,t,n,i,a);case 3:e:{if(W(t,t.stateNode.containerInfo),e===null)throw Error(r(387));n=t.pendingProps;var s=t.memoizedState;i=s.element,jc(e,t),jl(t,n,null,a);var f=t.memoizedState;if(n=f.cache,va(t,Pe,n),n!==s.cache&&Rc(t,[Pe],a,!0),Dl(),n=f.element,s.isDehydrated)if(s={element:n,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){t=i1(e,t,n,a);break e}else if(n!==i){i=jt(Error(r(424)),t),Ml(i),t=i1(e,t,n,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Qe=Yt(e.firstChild),mt=t,ze=!0,Ka=null,Xt=!0,a=Vf(t,null,n,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(wl(),n===i){t=ua(e,t,a);break e}lt(e,t,n,a)}t=t.child}return t;case 26:return es(e,t),e===null?(a=hd(t.type,null,t.pendingProps,null))?t.memoizedState=a:ze||(a=t.type,e=t.pendingProps,n=ps(ve.current).createElement(a),n[ot]=t,n[gt]=e,st(n,a,e),et(n),t.stateNode=n):t.memoizedState=hd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return We(t),e===null&&ze&&(n=t.stateNode=od(t.type,t.pendingProps,ve.current),mt=t,Xt=!0,i=Qe,Da(t.type)?(Zu=i,Qe=Yt(n.firstChild)):Qe=i),lt(e,t,t.pendingProps.children,a),es(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&ze&&((i=n=Qe)&&(n=kh(n,t.type,t.pendingProps,Xt),n!==null?(t.stateNode=n,mt=t,Qe=Yt(n.firstChild),Xt=!1,i=!0):i=!1),i||Ja(t)),We(t),i=t.type,s=t.pendingProps,f=e!==null?e.memoizedProps:null,n=s.children,Vu(i,s)?n=null:f!==null&&Vu(i,f)&&(t.flags|=32),t.memoizedState!==null&&(i=Bc(e,t,ah,null,null,a),ai._currentValue=i),es(e,t),lt(e,t,n,a),t.child;case 6:return e===null&&ze&&((e=a=Qe)&&(a=qh(a,t.pendingProps,Xt),a!==null?(t.stateNode=a,mt=t,Qe=null,e=!0):e=!1),e||Ja(t)),null;case 13:return s1(e,t,a);case 4:return W(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=Qn(t,null,n,a):lt(e,t,n,a),t.child;case 11:return Pf(e,t,t.type,t.pendingProps,a);case 7:return lt(e,t,t.pendingProps,a),t.child;case 8:return lt(e,t,t.pendingProps.children,a),t.child;case 12:return lt(e,t,t.pendingProps.children,a),t.child;case 10:return n=t.pendingProps,va(t,t.type,n.value),lt(e,t,n.children,a),t.child;case 9:return i=t.type._context,n=t.pendingProps.children,Wa(t),i=ft(i),n=n(i),t.flags|=1,lt(e,t,n,a),t.child;case 14:return If(e,t,t.type,t.pendingProps,a);case 15:return e1(e,t,t.type,t.pendingProps,a);case 19:return u1(e,t,a);case 31:return n=t.pendingProps,a=t.mode,n={mode:n.mode,children:n.children},e===null?(a=ts(n,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=ta(e.child,n),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return t1(e,t,a);case 24:return Wa(t),n=ft(Pe),e===null?(i=zc(),i===null&&(i=qe,s=xc(),i.pooledCache=s,s.refCount++,s!==null&&(i.pooledCacheLanes|=a),i=s),t.memoizedState={parent:n,cache:i},Dc(t),va(t,Pe,i)):((e.lanes&a)!==0&&(jc(e,t),jl(t,null,null,a),Dl()),i=e.memoizedState,s=t.memoizedState,i.parent!==n?(i={parent:n,cache:n},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),va(t,Pe,n)):(n=s.cache,va(t,Pe,n),n!==i.cache&&Rc(t,[Pe],a,!0))),lt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function ra(e){e.flags|=4}function o1(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!vd(t)){if(t=kt.current,t!==null&&((Re&4194048)===Re?$t!==null:(Re&62914560)!==Re&&(Re&536870912)===0||t!==$t))throw zl=Lc,Xo;e.flags|=8192}}function as(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Br():536870912,e.lanes|=t,Kn|=t)}function Vl(e,t){if(!ze)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var n=null;a!==null;)a.alternate!==null&&(n=a),a=a.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Ye(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,n=0;if(t)for(var i=e.child;i!==null;)a|=i.lanes|i.childLanes,n|=i.subtreeFlags&65011712,n|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)a|=i.lanes|i.childLanes,n|=i.subtreeFlags,n|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=n,e.childLanes=a,t}function fh(e,t,a){var n=t.pendingProps;switch(Tc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ye(t),null;case 1:return Ye(t),null;case 3:return a=t.stateNode,n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),ia(Pe),Ee(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Tl(t)?ra(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Vo())),Ye(t),null;case 26:return a=t.memoizedState,e===null?(ra(t),a!==null?(Ye(t),o1(t,a)):(Ye(t),t.flags&=-16777217)):a?a!==e.memoizedState?(ra(t),Ye(t),o1(t,a)):(Ye(t),t.flags&=-16777217):(e.memoizedProps!==n&&ra(t),Ye(t),t.flags&=-16777217),null;case 27:rt(t),a=ve.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==n&&ra(t);else{if(!n){if(t.stateNode===null)throw Error(r(166));return Ye(t),null}e=se.current,Tl(t)?qo(t):(e=od(i,n,a),t.stateNode=e,ra(t))}return Ye(t),null;case 5:if(rt(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==n&&ra(t);else{if(!n){if(t.stateNode===null)throw Error(r(166));return Ye(t),null}if(e=se.current,Tl(t))qo(t);else{switch(i=ps(ve.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof n.is=="string"?i.createElement("select",{is:n.is}):i.createElement("select"),n.multiple?e.multiple=!0:n.size&&(e.size=n.size);break;default:e=typeof n.is=="string"?i.createElement(a,{is:n.is}):i.createElement(a)}}e[ot]=t,e[gt]=n;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(st(e,a,n),a){case"button":case"input":case"select":case"textarea":e=!!n.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&ra(t)}}return Ye(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==n&&ra(t);else{if(typeof n!="string"&&t.stateNode===null)throw Error(r(166));if(e=ve.current,Tl(t)){if(e=t.stateNode,a=t.memoizedProps,n=null,i=mt,i!==null)switch(i.tag){case 27:case 5:n=i.memoizedProps}e[ot]=t,e=!!(e.nodeValue===a||n!==null&&n.suppressHydrationWarning===!0||nd(e.nodeValue,a)),e||Ja(t)}else e=ps(e).createTextNode(n),e[ot]=t,t.stateNode=e}return Ye(t),null;case 13:if(n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=Tl(t),n!==null&&n.dehydrated!==null){if(e===null){if(!i)throw Error(r(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(r(317));i[ot]=t}else wl(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ye(t),i=!1}else i=Vo(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(ca(t),t):(ca(t),null)}if(ca(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=n!==null,e=e!==null&&e.memoizedState!==null,a){n=t.child,i=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(i=n.alternate.memoizedState.cachePool.pool);var s=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(s=n.memoizedState.cachePool.pool),s!==i&&(n.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),as(t,t.updateQueue),Ye(t),null;case 4:return Ee(),e===null&&Uu(t.stateNode.containerInfo),Ye(t),null;case 10:return ia(t.type),Ye(t),null;case 19:if(I(Ie),i=t.memoizedState,i===null)return Ye(t),null;if(n=(t.flags&128)!==0,s=i.rendering,s===null)if(n)Vl(i,!1);else{if(Ze!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(s=Wi(e),s!==null){for(t.flags|=128,Vl(i,!1),e=s.updateQueue,t.updateQueue=e,as(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Ho(a,e),a=a.sibling;return F(Ie,Ie.current&1|2),t.child}e=e.sibling}i.tail!==null&&Zt()>is&&(t.flags|=128,n=!0,Vl(i,!1),t.lanes=4194304)}else{if(!n)if(e=Wi(s),e!==null){if(t.flags|=128,n=!0,e=e.updateQueue,t.updateQueue=e,as(t,e),Vl(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!ze)return Ye(t),null}else 2*Zt()-i.renderingStartTime>is&&a!==536870912&&(t.flags|=128,n=!0,Vl(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(e=i.last,e!==null?e.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Zt(),t.sibling=null,e=Ie.current,F(Ie,n?e&1|2:e&1),t):(Ye(t),null);case 22:case 23:return ca(t),kc(),n=t.memoizedState!==null,e!==null?e.memoizedState!==null!==n&&(t.flags|=8192):n&&(t.flags|=8192),n?(a&536870912)!==0&&(t.flags&128)===0&&(Ye(t),t.subtreeFlags&6&&(t.flags|=8192)):Ye(t),a=t.updateQueue,a!==null&&as(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),n=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),n!==a&&(t.flags|=2048),e!==null&&I(Pa),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),ia(Pe),Ye(t),null;case 25:return null;case 30:return null}throw Error(r(156,t.tag))}function dh(e,t){switch(Tc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ia(Pe),Ee(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return rt(t),null;case 13:if(ca(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(r(340));wl()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return I(Ie),null;case 4:return Ee(),null;case 10:return ia(t.type),null;case 22:case 23:return ca(t),kc(),e!==null&&I(Pa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return ia(Pe),null;case 25:return null;default:return null}}function f1(e,t){switch(Tc(t),t.tag){case 3:ia(Pe),Ee();break;case 26:case 27:case 5:rt(t);break;case 4:Ee();break;case 13:ca(t);break;case 19:I(Ie);break;case 10:ia(t.type);break;case 22:case 23:ca(t),kc(),e!==null&&I(Pa);break;case 24:ia(Pe)}}function Gl(e,t){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&e)===e){n=void 0;var s=a.create,f=a.inst;n=s(),f.destroy=n}a=a.next}while(a!==i)}}catch(h){ke(t,t.return,h)}}function wa(e,t,a){try{var n=t.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var s=i.next;n=s;do{if((n.tag&e)===e){var f=n.inst,h=f.destroy;if(h!==void 0){f.destroy=void 0,i=t;var y=a,N=h;try{N()}catch(_){ke(i,y,_)}}}n=n.next}while(n!==s)}}catch(_){ke(t,t.return,_)}}function d1(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Po(t,a)}catch(n){ke(e,e.return,n)}}}function h1(e,t,a){a.props=en(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(n){ke(e,t,n)}}function Yl(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var n=e.stateNode;break;case 30:n=e.stateNode;break;default:n=e.stateNode}typeof a=="function"?e.refCleanup=a(n):a.current=n}}catch(i){ke(e,t,i)}}function Kt(e,t){var a=e.ref,n=e.refCleanup;if(a!==null)if(typeof n=="function")try{n()}catch(i){ke(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(i){ke(e,t,i)}else a.current=null}function m1(e){var t=e.type,a=e.memoizedProps,n=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break e;case"img":a.src?n.src=a.src:a.srcSet&&(n.srcset=a.srcSet)}}catch(i){ke(e,e.return,i)}}function du(e,t,a){try{var n=e.stateNode;Dh(n,e.type,a,t),n[gt]=t}catch(i){ke(e,e.return,i)}}function p1(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Da(e.type)||e.tag===4}function hu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||p1(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Da(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function mu(e,t,a){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=ms));else if(n!==4&&(n===27&&Da(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(mu(e,t,a),e=e.sibling;e!==null;)mu(e,t,a),e=e.sibling}function ns(e,t,a){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(n!==4&&(n===27&&Da(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(ns(e,t,a),e=e.sibling;e!==null;)ns(e,t,a),e=e.sibling}function g1(e){var t=e.stateNode,a=e.memoizedProps;try{for(var n=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);st(t,n,a),t[ot]=e,t[gt]=a}catch(s){ke(e,e.return,s)}}var oa=!1,$e=!1,pu=!1,y1=typeof WeakSet=="function"?WeakSet:Set,at=null;function hh(e,t){if(e=e.containerInfo,qu=Ss,e=No(e),hc(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var n=a.getSelection&&a.getSelection();if(n&&n.rangeCount!==0){a=n.anchorNode;var i=n.anchorOffset,s=n.focusNode;n=n.focusOffset;try{a.nodeType,s.nodeType}catch(fe){a=null;break e}var f=0,h=-1,y=-1,N=0,_=0,B=e,x=null;t:for(;;){for(var z;B!==a||i!==0&&B.nodeType!==3||(h=f+i),B!==s||n!==0&&B.nodeType!==3||(y=f+n),B.nodeType===3&&(f+=B.nodeValue.length),(z=B.firstChild)!==null;)x=B,B=z;for(;;){if(B===e)break t;if(x===a&&++N===i&&(h=f),x===s&&++_===n&&(y=f),(z=B.nextSibling)!==null)break;B=x,x=B.parentNode}B=z}a=h===-1||y===-1?null:{start:h,end:y}}else a=null}a=a||{start:0,end:0}}else a=null;for(Bu={focusedElem:e,selectionRange:a},Ss=!1,at=t;at!==null;)if(t=at,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,at=e;else for(;at!==null;){switch(t=at,s=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&s!==null){e=void 0,a=t,i=s.memoizedProps,s=s.memoizedState,n=a.stateNode;try{var ye=en(a.type,i,a.elementType===a.type);e=n.getSnapshotBeforeUpdate(ye,s),n.__reactInternalSnapshotBeforeUpdate=e}catch(fe){ke(a,a.return,fe)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Yu(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Yu(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(r(163))}if(e=t.sibling,e!==null){e.return=t.return,at=e;break}at=t.return}}function v1(e,t,a){var n=a.flags;switch(a.tag){case 0:case 11:case 15:Ma(e,a),n&4&&Gl(5,a);break;case 1:if(Ma(e,a),n&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(f){ke(a,a.return,f)}else{var i=en(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){ke(a,a.return,f)}}n&64&&d1(a),n&512&&Yl(a,a.return);break;case 3:if(Ma(e,a),n&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Po(e,t)}catch(f){ke(a,a.return,f)}}break;case 27:t===null&&n&4&&g1(a);case 26:case 5:Ma(e,a),t===null&&n&4&&m1(a),n&512&&Yl(a,a.return);break;case 12:Ma(e,a);break;case 13:Ma(e,a),n&4&&S1(e,a),n&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Eh.bind(null,a),Bh(e,a))));break;case 22:if(n=a.memoizedState!==null||oa,!n){t=t!==null&&t.memoizedState!==null||$e,i=oa;var s=$e;oa=n,($e=t)&&!s?Na(e,a,(a.subtreeFlags&8772)!==0):Ma(e,a),oa=i,$e=s}break;case 30:break;default:Ma(e,a)}}function b1(e){var t=e.alternate;t!==null&&(e.alternate=null,b1(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Ks(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ge=null,bt=!1;function fa(e,t,a){for(a=a.child;a!==null;)C1(e,t,a),a=a.sibling}function C1(e,t,a){if(At&&typeof At.onCommitFiberUnmount=="function")try{At.onCommitFiberUnmount(ol,a)}catch(s){}switch(a.tag){case 26:$e||Kt(a,t),fa(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:$e||Kt(a,t);var n=Ge,i=bt;Da(a.type)&&(Ge=a.stateNode,bt=!1),fa(e,t,a),Pl(a.stateNode),Ge=n,bt=i;break;case 5:$e||Kt(a,t);case 6:if(n=Ge,i=bt,Ge=null,fa(e,t,a),Ge=n,bt=i,Ge!==null)if(bt)try{(Ge.nodeType===9?Ge.body:Ge.nodeName==="HTML"?Ge.ownerDocument.body:Ge).removeChild(a.stateNode)}catch(s){ke(a,t,s)}else try{Ge.removeChild(a.stateNode)}catch(s){ke(a,t,s)}break;case 18:Ge!==null&&(bt?(e=Ge,ud(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),si(e)):ud(Ge,a.stateNode));break;case 4:n=Ge,i=bt,Ge=a.stateNode.containerInfo,bt=!0,fa(e,t,a),Ge=n,bt=i;break;case 0:case 11:case 14:case 15:$e||wa(2,a,t),$e||wa(4,a,t),fa(e,t,a);break;case 1:$e||(Kt(a,t),n=a.stateNode,typeof n.componentWillUnmount=="function"&&h1(a,t,n)),fa(e,t,a);break;case 21:fa(e,t,a);break;case 22:$e=(n=$e)||a.memoizedState!==null,fa(e,t,a),$e=n;break;default:fa(e,t,a)}}function S1(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{si(e)}catch(a){ke(t,t.return,a)}}function mh(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new y1),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new y1),t;default:throw Error(r(435,e.tag))}}function gu(e,t){var a=mh(e);t.forEach(function(n){var i=Ah.bind(null,e,n);a.has(n)||(a.add(n),n.then(i,i))})}function Nt(e,t){var a=t.deletions;if(a!==null)for(var n=0;n<a.length;n++){var i=a[n],s=e,f=t,h=f;e:for(;h!==null;){switch(h.tag){case 27:if(Da(h.type)){Ge=h.stateNode,bt=!1;break e}break;case 5:Ge=h.stateNode,bt=!1;break e;case 3:case 4:Ge=h.stateNode.containerInfo,bt=!0;break e}h=h.return}if(Ge===null)throw Error(r(160));C1(s,f,i),Ge=null,bt=!1,s=i.alternate,s!==null&&(s.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)E1(t,e),t=t.sibling}var Gt=null;function E1(e,t){var a=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Nt(t,e),Rt(e),n&4&&(wa(3,e,e.return),Gl(3,e),wa(5,e,e.return));break;case 1:Nt(t,e),Rt(e),n&512&&($e||a===null||Kt(a,a.return)),n&64&&oa&&(e=e.updateQueue,e!==null&&(n=e.callbacks,n!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?n:a.concat(n))));break;case 26:var i=Gt;if(Nt(t,e),Rt(e),n&512&&($e||a===null||Kt(a,a.return)),n&4){var s=a!==null?a.memoizedState:null;if(n=e.memoizedState,a===null)if(n===null)if(e.stateNode===null){e:{n=e.type,a=e.memoizedProps,i=i.ownerDocument||i;t:switch(n){case"title":s=i.getElementsByTagName("title")[0],(!s||s[hl]||s[ot]||s.namespaceURI==="http://www.w3.org/2000/svg"||s.hasAttribute("itemprop"))&&(s=i.createElement(n),i.head.insertBefore(s,i.querySelector("head > title"))),st(s,n,a),s[ot]=e,et(s),n=s;break e;case"link":var f=gd("link","href",i).get(n+(a.href||""));if(f){for(var h=0;h<f.length;h++)if(s=f[h],s.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&s.getAttribute("rel")===(a.rel==null?null:a.rel)&&s.getAttribute("title")===(a.title==null?null:a.title)&&s.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(h,1);break t}}s=i.createElement(n),st(s,n,a),i.head.appendChild(s);break;case"meta":if(f=gd("meta","content",i).get(n+(a.content||""))){for(h=0;h<f.length;h++)if(s=f[h],s.getAttribute("content")===(a.content==null?null:""+a.content)&&s.getAttribute("name")===(a.name==null?null:a.name)&&s.getAttribute("property")===(a.property==null?null:a.property)&&s.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&s.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(h,1);break t}}s=i.createElement(n),st(s,n,a),i.head.appendChild(s);break;default:throw Error(r(468,n))}s[ot]=e,et(s),n=s}e.stateNode=n}else yd(i,e.type,e.stateNode);else e.stateNode=pd(i,n,e.memoizedProps);else s!==n?(s===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):s.count--,n===null?yd(i,e.type,e.stateNode):pd(i,n,e.memoizedProps)):n===null&&e.stateNode!==null&&du(e,e.memoizedProps,a.memoizedProps)}break;case 27:Nt(t,e),Rt(e),n&512&&($e||a===null||Kt(a,a.return)),a!==null&&n&4&&du(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Nt(t,e),Rt(e),n&512&&($e||a===null||Kt(a,a.return)),e.flags&32){i=e.stateNode;try{Mn(i,"")}catch(z){ke(e,e.return,z)}}n&4&&e.stateNode!=null&&(i=e.memoizedProps,du(e,i,a!==null?a.memoizedProps:i)),n&1024&&(pu=!0);break;case 6:if(Nt(t,e),Rt(e),n&4){if(e.stateNode===null)throw Error(r(162));n=e.memoizedProps,a=e.stateNode;try{a.nodeValue=n}catch(z){ke(e,e.return,z)}}break;case 3:if(vs=null,i=Gt,Gt=gs(t.containerInfo),Nt(t,e),Gt=i,Rt(e),n&4&&a!==null&&a.memoizedState.isDehydrated)try{si(t.containerInfo)}catch(z){ke(e,e.return,z)}pu&&(pu=!1,A1(e));break;case 4:n=Gt,Gt=gs(e.stateNode.containerInfo),Nt(t,e),Rt(e),Gt=n;break;case 12:Nt(t,e),Rt(e);break;case 13:Nt(t,e),Rt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Eu=Zt()),n&4&&(n=e.updateQueue,n!==null&&(e.updateQueue=null,gu(e,n)));break;case 22:i=e.memoizedState!==null;var y=a!==null&&a.memoizedState!==null,N=oa,_=$e;if(oa=N||i,$e=_||y,Nt(t,e),$e=_,oa=N,Rt(e),n&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(a===null||y||oa||$e||tn(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){y=a=t;try{if(s=y.stateNode,i)f=s.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{h=y.stateNode;var B=y.memoizedProps.style,x=B!=null&&B.hasOwnProperty("display")?B.display:null;h.style.display=x==null||typeof x=="boolean"?"":(""+x).trim()}}catch(z){ke(y,y.return,z)}}}else if(t.tag===6){if(a===null){y=t;try{y.stateNode.nodeValue=i?"":y.memoizedProps}catch(z){ke(y,y.return,z)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}n&4&&(n=e.updateQueue,n!==null&&(a=n.retryQueue,a!==null&&(n.retryQueue=null,gu(e,a))));break;case 19:Nt(t,e),Rt(e),n&4&&(n=e.updateQueue,n!==null&&(e.updateQueue=null,gu(e,n)));break;case 30:break;case 21:break;default:Nt(t,e),Rt(e)}}function Rt(e){var t=e.flags;if(t&2){try{for(var a,n=e.return;n!==null;){if(p1(n)){a=n;break}n=n.return}if(a==null)throw Error(r(160));switch(a.tag){case 27:var i=a.stateNode,s=hu(e);ns(e,s,i);break;case 5:var f=a.stateNode;a.flags&32&&(Mn(f,""),a.flags&=-33);var h=hu(e);ns(e,h,f);break;case 3:case 4:var y=a.stateNode.containerInfo,N=hu(e);mu(e,N,y);break;default:throw Error(r(161))}}catch(_){ke(e,e.return,_)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function A1(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;A1(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Ma(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)v1(e,t.alternate,t),t=t.sibling}function tn(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:wa(4,t,t.return),tn(t);break;case 1:Kt(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&h1(t,t.return,a),tn(t);break;case 27:Pl(t.stateNode);case 26:case 5:Kt(t,t.return),tn(t);break;case 22:t.memoizedState===null&&tn(t);break;case 30:tn(t);break;default:tn(t)}e=e.sibling}}function Na(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var n=t.alternate,i=e,s=t,f=s.flags;switch(s.tag){case 0:case 11:case 15:Na(i,s,a),Gl(4,s);break;case 1:if(Na(i,s,a),n=s,i=n.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(N){ke(n,n.return,N)}if(n=s,i=n.updateQueue,i!==null){var h=n.stateNode;try{var y=i.shared.hiddenCallbacks;if(y!==null)for(i.shared.hiddenCallbacks=null,i=0;i<y.length;i++)Wo(y[i],h)}catch(N){ke(n,n.return,N)}}a&&f&64&&d1(s),Yl(s,s.return);break;case 27:g1(s);case 26:case 5:Na(i,s,a),a&&n===null&&f&4&&m1(s),Yl(s,s.return);break;case 12:Na(i,s,a);break;case 13:Na(i,s,a),a&&f&4&&S1(i,s);break;case 22:s.memoizedState===null&&Na(i,s,a),Yl(s,s.return);break;case 30:break;default:Na(i,s,a)}t=t.sibling}}function yu(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Rl(a))}function vu(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Rl(e))}function Jt(e,t,a,n){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)T1(e,t,a,n),t=t.sibling}function T1(e,t,a,n){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Jt(e,t,a,n),i&2048&&Gl(9,t);break;case 1:Jt(e,t,a,n);break;case 3:Jt(e,t,a,n),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Rl(e)));break;case 12:if(i&2048){Jt(e,t,a,n),e=t.stateNode;try{var s=t.memoizedProps,f=s.id,h=s.onPostCommit;typeof h=="function"&&h(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(y){ke(t,t.return,y)}}else Jt(e,t,a,n);break;case 13:Jt(e,t,a,n);break;case 23:break;case 22:s=t.stateNode,f=t.alternate,t.memoizedState!==null?s._visibility&2?Jt(e,t,a,n):Ql(e,t):s._visibility&2?Jt(e,t,a,n):(s._visibility|=2,Zn(e,t,a,n,(t.subtreeFlags&10256)!==0)),i&2048&&yu(f,t);break;case 24:Jt(e,t,a,n),i&2048&&vu(t.alternate,t);break;default:Jt(e,t,a,n)}}function Zn(e,t,a,n,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var s=e,f=t,h=a,y=n,N=f.flags;switch(f.tag){case 0:case 11:case 15:Zn(s,f,h,y,i),Gl(8,f);break;case 23:break;case 22:var _=f.stateNode;f.memoizedState!==null?_._visibility&2?Zn(s,f,h,y,i):Ql(s,f):(_._visibility|=2,Zn(s,f,h,y,i)),i&&N&2048&&yu(f.alternate,f);break;case 24:Zn(s,f,h,y,i),i&&N&2048&&vu(f.alternate,f);break;default:Zn(s,f,h,y,i)}t=t.sibling}}function Ql(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,n=t,i=n.flags;switch(n.tag){case 22:Ql(a,n),i&2048&&yu(n.alternate,n);break;case 24:Ql(a,n),i&2048&&vu(n.alternate,n);break;default:Ql(a,n)}t=t.sibling}}var Zl=8192;function Xn(e){if(e.subtreeFlags&Zl)for(e=e.child;e!==null;)w1(e),e=e.sibling}function w1(e){switch(e.tag){case 26:Xn(e),e.flags&Zl&&e.memoizedState!==null&&Ih(Gt,e.memoizedState,e.memoizedProps);break;case 5:Xn(e);break;case 3:case 4:var t=Gt;Gt=gs(e.stateNode.containerInfo),Xn(e),Gt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Zl,Zl=16777216,Xn(e),Zl=t):Xn(e));break;default:Xn(e)}}function M1(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Xl(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var n=t[a];at=n,R1(n,e)}M1(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)N1(e),e=e.sibling}function N1(e){switch(e.tag){case 0:case 11:case 15:Xl(e),e.flags&2048&&wa(9,e,e.return);break;case 3:Xl(e);break;case 12:Xl(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,ls(e)):Xl(e);break;default:Xl(e)}}function ls(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var n=t[a];at=n,R1(n,e)}M1(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:wa(8,t,t.return),ls(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,ls(t));break;default:ls(t)}e=e.sibling}}function R1(e,t){for(;at!==null;){var a=at;switch(a.tag){case 0:case 11:case 15:wa(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var n=a.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:Rl(a.memoizedState.cache)}if(n=a.child,n!==null)n.return=a,at=n;else e:for(a=e;at!==null;){n=at;var i=n.sibling,s=n.return;if(b1(n),n===a){at=null;break e}if(i!==null){i.return=s,at=i;break e}at=s}}}var ph={getCacheForType:function(e){var t=ft(Pe),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},gh=typeof WeakMap=="function"?WeakMap:Map,De=0,qe=null,we=null,Re=0,je=0,xt=null,Ra=!1,$n=!1,bu=!1,da=0,Ze=0,xa=0,an=0,Cu=0,qt=0,Kn=0,$l=null,Ct=null,Su=!1,Eu=0,is=1/0,ss=null,Oa=null,it=0,za=null,Jn=null,Fn=0,Au=0,Tu=null,x1=null,Kl=0,wu=null;function Ot(){if((De&2)!==0&&Re!==0)return Re&-Re;if(L.T!==null){var e=Hn;return e!==0?e:Lu()}return Yr()}function O1(){qt===0&&(qt=(Re&536870912)===0||ze?qr():536870912);var e=kt.current;return e!==null&&(e.flags|=32),qt}function zt(e,t,a){(e===qe&&(je===2||je===9)||e.cancelPendingCommit!==null)&&(Wn(e,0),La(e,Re,qt,!1)),dl(e,a),((De&2)===0||e!==qe)&&(e===qe&&((De&2)===0&&(an|=a),Ze===4&&La(e,Re,qt,!1)),Ft(e))}function z1(e,t,a){if((De&6)!==0)throw Error(r(327));var n=!a&&(t&124)===0&&(t&e.expiredLanes)===0||fl(e,t),i=n?bh(e,t):Ru(e,t,!0),s=n;do{if(i===0){$n&&!n&&La(e,t,0,!1);break}else{if(a=e.current.alternate,s&&!yh(a)){i=Ru(e,t,!1),s=!1;continue}if(i===2){if(s=t,e.errorRecoveryDisabledLanes&s)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var h=e;i=$l;var y=h.current.memoizedState.isDehydrated;if(y&&(Wn(h,f).flags|=256),f=Ru(h,f,!1),f!==2){if(bu&&!y){h.errorRecoveryDisabledLanes|=s,an|=s,i=4;break e}s=Ct,Ct=i,s!==null&&(Ct===null?Ct=s:Ct.push.apply(Ct,s))}i=f}if(s=!1,i!==2)continue}}if(i===1){Wn(e,0),La(e,t,0,!0);break}e:{switch(n=e,s=i,s){case 0:case 1:throw Error(r(345));case 4:if((t&4194048)!==t)break;case 6:La(n,t,qt,!Ra);break e;case 2:Ct=null;break;case 3:case 5:break;default:throw Error(r(329))}if((t&62914560)===t&&(i=Eu+300-Zt(),10<i)){if(La(n,t,qt,!Ra),yi(n,0,!0)!==0)break e;n.timeoutHandle=sd(L1.bind(null,n,a,Ct,ss,Su,t,qt,an,Kn,Ra,s,2,-0,0),i);break e}L1(n,a,Ct,ss,Su,t,qt,an,Kn,Ra,s,0,-0,0)}}break}while(!0);Ft(e)}function L1(e,t,a,n,i,s,f,h,y,N,_,B,x,z){if(e.timeoutHandle=-1,B=t.subtreeFlags,(B&8192||(B&16785408)===16785408)&&(ti={stylesheets:null,count:0,unsuspend:Ph},w1(t),B=em(),B!==null)){e.cancelPendingCommit=B(q1.bind(null,e,t,s,a,n,i,f,h,y,_,1,x,z)),La(e,s,f,!N);return}q1(e,t,s,a,n,i,f,h,y)}function yh(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var n=0;n<a.length;n++){var i=a[n],s=i.getSnapshot;i=i.value;try{if(!wt(s(),i))return!1}catch(f){return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function La(e,t,a,n){t&=~Cu,t&=~an,e.suspendedLanes|=t,e.pingedLanes&=~t,n&&(e.warmLanes|=t),n=e.expirationTimes;for(var i=t;0<i;){var s=31-Tt(i),f=1<<s;n[s]=-1,i&=~f}a!==0&&Vr(e,a,t)}function cs(){return(De&6)===0?(Jl(0),!1):!0}function Mu(){if(we!==null){if(je===0)var e=we.return;else e=we,la=Fa=null,Yc(e),Yn=null,ql=0,e=we;for(;e!==null;)f1(e.alternate,e),e=e.return;we=null}}function Wn(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,_h(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Mu(),qe=e,we=a=ta(e.current,null),Re=t,je=0,xt=null,Ra=!1,$n=fl(e,t),bu=!1,Kn=qt=Cu=an=xa=Ze=0,Ct=$l=null,Su=!1,(t&8)!==0&&(t|=t&32);var n=e.entangledLanes;if(n!==0)for(e=e.entanglements,n&=t;0<n;){var i=31-Tt(n),s=1<<i;t|=e[i],n&=~s}return da=t,xi(),a}function D1(e,t){Ae=null,L.H=Ki,t===Ol||t===ki?(t=Jo(),je=3):t===Xo?(t=Jo(),je=4):je=t===Wf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,xt=t,we===null&&(Ze=1,Ii(e,jt(t,e.current)))}function j1(){var e=L.H;return L.H=Ki,e===null?Ki:e}function _1(){var e=L.A;return L.A=ph,e}function Nu(){Ze=4,Ra||(Re&4194048)!==Re&&kt.current!==null||($n=!0),(xa&134217727)===0&&(an&134217727)===0||qe===null||La(qe,Re,qt,!1)}function Ru(e,t,a){var n=De;De|=2;var i=j1(),s=_1();(qe!==e||Re!==t)&&(ss=null,Wn(e,t)),t=!1;var f=Ze;e:do try{if(je!==0&&we!==null){var h=we,y=xt;switch(je){case 8:Mu(),f=6;break e;case 3:case 2:case 9:case 6:kt.current===null&&(t=!0);var N=je;if(je=0,xt=null,Pn(e,h,y,N),a&&$n){f=0;break e}break;default:N=je,je=0,xt=null,Pn(e,h,y,N)}}vh(),f=Ze;break}catch(_){D1(e,_)}while(!0);return t&&e.shellSuspendCounter++,la=Fa=null,De=n,L.H=i,L.A=s,we===null&&(qe=null,Re=0,xi()),f}function vh(){for(;we!==null;)U1(we)}function bh(e,t){var a=De;De|=2;var n=j1(),i=_1();qe!==e||Re!==t?(ss=null,is=Zt()+500,Wn(e,t)):$n=fl(e,t);e:do try{if(je!==0&&we!==null){t=we;var s=xt;t:switch(je){case 1:je=0,xt=null,Pn(e,t,s,1);break;case 2:case 9:if($o(s)){je=0,xt=null,H1(t);break}t=function(){je!==2&&je!==9||qe!==e||(je=7),Ft(e)},s.then(t,t);break e;case 3:je=7;break e;case 4:je=5;break e;case 7:$o(s)?(je=0,xt=null,H1(t)):(je=0,xt=null,Pn(e,t,s,7));break;case 5:var f=null;switch(we.tag){case 26:f=we.memoizedState;case 5:case 27:var h=we;if(!f||vd(f)){je=0,xt=null;var y=h.sibling;if(y!==null)we=y;else{var N=h.return;N!==null?(we=N,us(N)):we=null}break t}}je=0,xt=null,Pn(e,t,s,5);break;case 6:je=0,xt=null,Pn(e,t,s,6);break;case 8:Mu(),Ze=6;break e;default:throw Error(r(462))}}Ch();break}catch(_){D1(e,_)}while(!0);return la=Fa=null,L.H=n,L.A=i,De=a,we!==null?0:(qe=null,Re=0,xi(),Ze)}function Ch(){for(;we!==null&&!G2();)U1(we)}function U1(e){var t=r1(e.alternate,e,da);e.memoizedProps=e.pendingProps,t===null?us(e):we=t}function H1(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=n1(a,t,t.pendingProps,t.type,void 0,Re);break;case 11:t=n1(a,t,t.pendingProps,t.type.render,t.ref,Re);break;case 5:Yc(t);default:f1(a,t),t=we=Ho(t,da),t=r1(a,t,da)}e.memoizedProps=e.pendingProps,t===null?us(e):we=t}function Pn(e,t,a,n){la=Fa=null,Yc(t),Yn=null,ql=0;var i=t.return;try{if(rh(e,i,t,a,Re)){Ze=1,Ii(e,jt(a,e.current)),we=null;return}}catch(s){if(i!==null)throw we=i,s;Ze=1,Ii(e,jt(a,e.current)),we=null;return}t.flags&32768?(ze||n===1?e=!0:$n||(Re&536870912)!==0?e=!1:(Ra=e=!0,(n===2||n===9||n===3||n===6)&&(n=kt.current,n!==null&&n.tag===13&&(n.flags|=16384))),k1(t,e)):us(t)}function us(e){var t=e;do{if((t.flags&32768)!==0){k1(t,Ra);return}e=t.return;var a=fh(t.alternate,t,da);if(a!==null){we=a;return}if(t=t.sibling,t!==null){we=t;return}we=t=e}while(t!==null);Ze===0&&(Ze=5)}function k1(e,t){do{var a=dh(e.alternate,e);if(a!==null){a.flags&=32767,we=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){we=e;return}we=e=a}while(e!==null);Ze=6,we=null}function q1(e,t,a,n,i,s,f,h,y){e.cancelPendingCommit=null;do rs();while(it!==0);if((De&6)!==0)throw Error(r(327));if(t!==null){if(t===e.current)throw Error(r(177));if(s=t.lanes|t.childLanes,s|=vc,P2(e,a,s,f,h,y),e===qe&&(we=qe=null,Re=0),Jn=t,za=e,Fn=a,Au=s,Tu=i,x1=n,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Th(mi,function(){return Q1(),null})):(e.callbackNode=null,e.callbackPriority=0),n=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||n){n=L.T,L.T=null,i=$.p,$.p=2,f=De,De|=4;try{hh(e,t,a)}finally{De=f,$.p=i,L.T=n}}it=1,B1(),V1(),G1()}}function B1(){if(it===1){it=0;var e=za,t=Jn,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=L.T,L.T=null;var n=$.p;$.p=2;var i=De;De|=4;try{E1(t,e);var s=Bu,f=No(e.containerInfo),h=s.focusedElem,y=s.selectionRange;if(f!==h&&h&&h.ownerDocument&&Mo(h.ownerDocument.documentElement,h)){if(y!==null&&hc(h)){var N=y.start,_=y.end;if(_===void 0&&(_=N),"selectionStart"in h)h.selectionStart=N,h.selectionEnd=Math.min(_,h.value.length);else{var B=h.ownerDocument||document,x=B&&B.defaultView||window;if(x.getSelection){var z=x.getSelection(),ye=h.textContent.length,fe=Math.min(y.start,ye),He=y.end===void 0?fe:Math.min(y.end,ye);!z.extend&&fe>He&&(f=He,He=fe,fe=f);var T=wo(h,fe),E=wo(h,He);if(T&&E&&(z.rangeCount!==1||z.anchorNode!==T.node||z.anchorOffset!==T.offset||z.focusNode!==E.node||z.focusOffset!==E.offset)){var M=B.createRange();M.setStart(T.node,T.offset),z.removeAllRanges(),fe>He?(z.addRange(M),z.extend(E.node,E.offset)):(M.setEnd(E.node,E.offset),z.addRange(M))}}}}for(B=[],z=h;z=z.parentNode;)z.nodeType===1&&B.push({element:z,left:z.scrollLeft,top:z.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<B.length;h++){var U=B[h];U.element.scrollLeft=U.left,U.element.scrollTop=U.top}}Ss=!!qu,Bu=qu=null}finally{De=i,$.p=n,L.T=a}}e.current=t,it=2}}function V1(){if(it===2){it=0;var e=za,t=Jn,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=L.T,L.T=null;var n=$.p;$.p=2;var i=De;De|=4;try{v1(e,t.alternate,t)}finally{De=i,$.p=n,L.T=a}}it=3}}function G1(){if(it===4||it===3){it=0,Y2();var e=za,t=Jn,a=Fn,n=x1;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?it=5:(it=0,Jn=za=null,Y1(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(Oa=null),Xs(a),t=t.stateNode,At&&typeof At.onCommitFiberRoot=="function")try{At.onCommitFiberRoot(ol,t,void 0,(t.current.flags&128)===128)}catch(y){}if(n!==null){t=L.T,i=$.p,$.p=2,L.T=null;try{for(var s=e.onRecoverableError,f=0;f<n.length;f++){var h=n[f];s(h.value,{componentStack:h.stack})}}finally{L.T=t,$.p=i}}(Fn&3)!==0&&rs(),Ft(e),i=e.pendingLanes,(a&4194090)!==0&&(i&42)!==0?e===wu?Kl++:(Kl=0,wu=e):Kl=0,Jl(0)}}function Y1(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Rl(t)))}function rs(e){return B1(),V1(),G1(),Q1()}function Q1(){if(it!==5)return!1;var e=za,t=Au;Au=0;var a=Xs(Fn),n=L.T,i=$.p;try{$.p=32>a?32:a,L.T=null,a=Tu,Tu=null;var s=za,f=Fn;if(it=0,Jn=za=null,Fn=0,(De&6)!==0)throw Error(r(331));var h=De;if(De|=4,N1(s.current),T1(s,s.current,f,a),De=h,Jl(0,!1),At&&typeof At.onPostCommitFiberRoot=="function")try{At.onPostCommitFiberRoot(ol,s)}catch(y){}return!0}finally{$.p=i,L.T=n,Y1(e,t)}}function Z1(e,t,a){t=jt(a,t),t=nu(e.stateNode,t,2),e=Sa(e,t,2),e!==null&&(dl(e,2),Ft(e))}function ke(e,t,a){if(e.tag===3)Z1(e,e,a);else for(;t!==null;){if(t.tag===3){Z1(t,e,a);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Oa===null||!Oa.has(n))){e=jt(a,e),a=Jf(2),n=Sa(t,a,2),n!==null&&(Ff(a,n,t,e),dl(n,2),Ft(n));break}}t=t.return}}function xu(e,t,a){var n=e.pingCache;if(n===null){n=e.pingCache=new gh;var i=new Set;n.set(t,i)}else i=n.get(t),i===void 0&&(i=new Set,n.set(t,i));i.has(a)||(bu=!0,i.add(a),e=Sh.bind(null,e,t,a),t.then(e,e))}function Sh(e,t,a){var n=e.pingCache;n!==null&&n.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,qe===e&&(Re&a)===a&&(Ze===4||Ze===3&&(Re&62914560)===Re&&300>Zt()-Eu?(De&2)===0&&Wn(e,0):Cu|=a,Kn===Re&&(Kn=0)),Ft(e)}function X1(e,t){t===0&&(t=Br()),e=Dn(e,t),e!==null&&(dl(e,t),Ft(e))}function Eh(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),X1(e,a)}function Ah(e,t){var a=0;switch(e.tag){case 13:var n=e.stateNode,i=e.memoizedState;i!==null&&(a=i.retryLane);break;case 19:n=e.stateNode;break;case 22:n=e.stateNode._retryCache;break;default:throw Error(r(314))}n!==null&&n.delete(t),X1(e,a)}function Th(e,t){return Gs(e,t)}var os=null,In=null,Ou=!1,fs=!1,zu=!1,nn=0;function Ft(e){e!==In&&e.next===null&&(In===null?os=In=e:In=In.next=e),fs=!0,Ou||(Ou=!0,Mh())}function Jl(e,t){if(!zu&&fs){zu=!0;do for(var a=!1,n=os;n!==null;){if(e!==0){var i=n.pendingLanes;if(i===0)var s=0;else{var f=n.suspendedLanes,h=n.pingedLanes;s=(1<<31-Tt(42|e)+1)-1,s&=i&~(f&~h),s=s&201326741?s&201326741|1:s?s|2:0}s!==0&&(a=!0,F1(n,s))}else s=Re,s=yi(n,n===qe?s:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(s&3)===0||fl(n,s)||(a=!0,F1(n,s));n=n.next}while(a);zu=!1}}function wh(){$1()}function $1(){fs=Ou=!1;var e=0;nn!==0&&(jh()&&(e=nn),nn=0);for(var t=Zt(),a=null,n=os;n!==null;){var i=n.next,s=K1(n,t);s===0?(n.next=null,a===null?os=i:a.next=i,i===null&&(In=a)):(a=n,(e!==0||(s&3)!==0)&&(fs=!0)),n=i}Jl(e)}function K1(e,t){for(var a=e.suspendedLanes,n=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes&-62914561;0<s;){var f=31-Tt(s),h=1<<f,y=i[f];y===-1?((h&a)===0||(h&n)!==0)&&(i[f]=W2(h,t)):y<=t&&(e.expiredLanes|=h),s&=~h}if(t=qe,a=Re,a=yi(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),n=e.callbackNode,a===0||e===t&&(je===2||je===9)||e.cancelPendingCommit!==null)return n!==null&&n!==null&&Ys(n),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||fl(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(n!==null&&Ys(n),Xs(a)){case 2:case 8:a=Hr;break;case 32:a=mi;break;case 268435456:a=kr;break;default:a=mi}return n=J1.bind(null,e),a=Gs(a,n),e.callbackPriority=t,e.callbackNode=a,t}return n!==null&&n!==null&&Ys(n),e.callbackPriority=2,e.callbackNode=null,2}function J1(e,t){if(it!==0&&it!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(rs()&&e.callbackNode!==a)return null;var n=Re;return n=yi(e,e===qe?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),n===0?null:(z1(e,n,t),K1(e,Zt()),e.callbackNode!=null&&e.callbackNode===a?J1.bind(null,e):null)}function F1(e,t){if(rs())return null;z1(e,t,!0)}function Mh(){Uh(function(){(De&6)!==0?Gs(Ur,wh):$1()})}function Lu(){return nn===0&&(nn=qr()),nn}function W1(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Ei(""+e)}function P1(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Nh(e,t,a,n,i){if(t==="submit"&&a&&a.stateNode===i){var s=W1((i[gt]||null).action),f=n.submitter;f&&(t=(t=f[gt]||null)?W1(t.formAction):f.getAttribute("formAction"),t!==null&&(s=t,f=null));var h=new Mi("action","action",null,n,i);e.push({event:h,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(nn!==0){var y=f?P1(i,f):new FormData(i);Pc(a,{pending:!0,data:y,method:i.method,action:s},null,y)}}else typeof s=="function"&&(h.preventDefault(),y=f?P1(i,f):new FormData(i),Pc(a,{pending:!0,data:y,method:i.method,action:s},s,y))},currentTarget:i}]})}}for(var Du=0;Du<yc.length;Du++){var ju=yc[Du],Rh=ju.toLowerCase(),xh=ju[0].toUpperCase()+ju.slice(1);Vt(Rh,"on"+xh)}Vt(Oo,"onAnimationEnd"),Vt(zo,"onAnimationIteration"),Vt(Lo,"onAnimationStart"),Vt("dblclick","onDoubleClick"),Vt("focusin","onFocus"),Vt("focusout","onBlur"),Vt(X0,"onTransitionRun"),Vt($0,"onTransitionStart"),Vt(K0,"onTransitionCancel"),Vt(Do,"onTransitionEnd"),An("onMouseEnter",["mouseout","mouseover"]),An("onMouseLeave",["mouseout","mouseover"]),An("onPointerEnter",["pointerout","pointerover"]),An("onPointerLeave",["pointerout","pointerover"]),Va("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Va("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Va("onBeforeInput",["compositionend","keypress","textInput","paste"]),Va("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Va("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Va("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Oh=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Fl));function I1(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var n=e[a],i=n.event;n=n.listeners;e:{var s=void 0;if(t)for(var f=n.length-1;0<=f;f--){var h=n[f],y=h.instance,N=h.currentTarget;if(h=h.listener,y!==s&&i.isPropagationStopped())break e;s=h,i.currentTarget=N;try{s(i)}catch(_){Pi(_)}i.currentTarget=null,s=y}else for(f=0;f<n.length;f++){if(h=n[f],y=h.instance,N=h.currentTarget,h=h.listener,y!==s&&i.isPropagationStopped())break e;s=h,i.currentTarget=N;try{s(i)}catch(_){Pi(_)}i.currentTarget=null,s=y}}}}function Me(e,t){var a=t[$s];a===void 0&&(a=t[$s]=new Set);var n=e+"__bubble";a.has(n)||(ed(t,e,2,!1),a.add(n))}function _u(e,t,a){var n=0;t&&(n|=4),ed(a,e,n,t)}var ds="_reactListening"+Math.random().toString(36).slice(2);function Uu(e){if(!e[ds]){e[ds]=!0,Zr.forEach(function(a){a!=="selectionchange"&&(Oh.has(a)||_u(a,!1,e),_u(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ds]||(t[ds]=!0,_u("selectionchange",!1,t))}}function ed(e,t,a,n){switch(Td(t)){case 2:var i=nm;break;case 8:i=lm;break;default:i=Fu}a=i.bind(null,t,a,e),i=void 0,!lc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),n?i!==void 0?e.addEventListener(t,a,{capture:!0,passive:i}):e.addEventListener(t,a,!0):i!==void 0?e.addEventListener(t,a,{passive:i}):e.addEventListener(t,a,!1)}function Hu(e,t,a,n,i){var s=n;if((t&1)===0&&(t&2)===0&&n!==null)e:for(;;){if(n===null)return;var f=n.tag;if(f===3||f===4){var h=n.stateNode.containerInfo;if(h===i)break;if(f===4)for(f=n.return;f!==null;){var y=f.tag;if((y===3||y===4)&&f.stateNode.containerInfo===i)return;f=f.return}for(;h!==null;){if(f=Cn(h),f===null)return;if(y=f.tag,y===5||y===6||y===26||y===27){n=s=f;continue e}h=h.parentNode}}n=n.return}io(function(){var N=s,_=ac(a),B=[];e:{var x=jo.get(e);if(x!==void 0){var z=Mi,ye=e;switch(e){case"keypress":if(Ti(a)===0)break e;case"keydown":case"keyup":z=T0;break;case"focusin":ye="focus",z=uc;break;case"focusout":ye="blur",z=uc;break;case"beforeblur":case"afterblur":z=uc;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":z=uo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":z=d0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":z=N0;break;case Oo:case zo:case Lo:z=p0;break;case Do:z=x0;break;case"scroll":case"scrollend":z=o0;break;case"wheel":z=z0;break;case"copy":case"cut":case"paste":z=y0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":z=oo;break;case"toggle":case"beforetoggle":z=D0}var fe=(t&4)!==0,He=!fe&&(e==="scroll"||e==="scrollend"),T=fe?x!==null?x+"Capture":null:x;fe=[];for(var E=N,M;E!==null;){var U=E;if(M=U.stateNode,U=U.tag,U!==5&&U!==26&&U!==27||M===null||T===null||(U=pl(E,T),U!=null&&fe.push(Wl(E,U,M))),He)break;E=E.return}0<fe.length&&(x=new z(x,ye,null,a,_),B.push({event:x,listeners:fe}))}}if((t&7)===0){e:{if(x=e==="mouseover"||e==="pointerover",z=e==="mouseout"||e==="pointerout",x&&a!==tc&&(ye=a.relatedTarget||a.fromElement)&&(Cn(ye)||ye[bn]))break e;if((z||x)&&(x=_.window===_?_:(x=_.ownerDocument)?x.defaultView||x.parentWindow:window,z?(ye=a.relatedTarget||a.toElement,z=N,ye=ye?Cn(ye):null,ye!==null&&(He=d(ye),fe=ye.tag,ye!==He||fe!==5&&fe!==27&&fe!==6)&&(ye=null)):(z=null,ye=N),z!==ye)){if(fe=uo,U="onMouseLeave",T="onMouseEnter",E="mouse",(e==="pointerout"||e==="pointerover")&&(fe=oo,U="onPointerLeave",T="onPointerEnter",E="pointer"),He=z==null?x:ml(z),M=ye==null?x:ml(ye),x=new fe(U,E+"leave",z,a,_),x.target=He,x.relatedTarget=M,U=null,Cn(_)===N&&(fe=new fe(T,E+"enter",ye,a,_),fe.target=M,fe.relatedTarget=He,U=fe),He=U,z&&ye)t:{for(fe=z,T=ye,E=0,M=fe;M;M=el(M))E++;for(M=0,U=T;U;U=el(U))M++;for(;0<E-M;)fe=el(fe),E--;for(;0<M-E;)T=el(T),M--;for(;E--;){if(fe===T||T!==null&&fe===T.alternate)break t;fe=el(fe),T=el(T)}fe=null}else fe=null;z!==null&&td(B,x,z,fe,!1),ye!==null&&He!==null&&td(B,He,ye,fe,!0)}}e:{if(x=N?ml(N):window,z=x.nodeName&&x.nodeName.toLowerCase(),z==="select"||z==="input"&&x.type==="file")var ae=bo;else if(yo(x))if(Co)ae=Y0;else{ae=V0;var Te=B0}else z=x.nodeName,!z||z.toLowerCase()!=="input"||x.type!=="checkbox"&&x.type!=="radio"?N&&ec(N.elementType)&&(ae=bo):ae=G0;if(ae&&(ae=ae(e,N))){vo(B,ae,a,_);break e}Te&&Te(e,x,N),e==="focusout"&&N&&x.type==="number"&&N.memoizedProps.value!=null&&Is(x,"number",x.value)}switch(Te=N?ml(N):window,e){case"focusin":(yo(Te)||Te.contentEditable==="true")&&(On=Te,mc=N,Al=null);break;case"focusout":Al=mc=On=null;break;case"mousedown":pc=!0;break;case"contextmenu":case"mouseup":case"dragend":pc=!1,Ro(B,a,_);break;case"selectionchange":if(Z0)break;case"keydown":case"keyup":Ro(B,a,_)}var ie;if(oc)e:{switch(e){case"compositionstart":var he="onCompositionStart";break e;case"compositionend":he="onCompositionEnd";break e;case"compositionupdate":he="onCompositionUpdate";break e}he=void 0}else xn?po(e,a)&&(he="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(he="onCompositionStart");he&&(fo&&a.locale!=="ko"&&(xn||he!=="onCompositionStart"?he==="onCompositionEnd"&&xn&&(ie=so()):(ya=_,ic="value"in ya?ya.value:ya.textContent,xn=!0)),Te=hs(N,he),0<Te.length&&(he=new ro(he,e,null,a,_),B.push({event:he,listeners:Te}),ie?he.data=ie:(ie=go(a),ie!==null&&(he.data=ie)))),(ie=_0?U0(e,a):H0(e,a))&&(he=hs(N,"onBeforeInput"),0<he.length&&(Te=new ro("onBeforeInput","beforeinput",null,a,_),B.push({event:Te,listeners:he}),Te.data=ie)),Nh(B,e,N,a,_)}I1(B,t)})}function Wl(e,t,a){return{instance:e,listener:t,currentTarget:a}}function hs(e,t){for(var a=t+"Capture",n=[];e!==null;){var i=e,s=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||s===null||(i=pl(e,a),i!=null&&n.unshift(Wl(e,i,s)),i=pl(e,t),i!=null&&n.push(Wl(e,i,s))),e.tag===3)return n;e=e.return}return[]}function el(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function td(e,t,a,n,i){for(var s=t._reactName,f=[];a!==null&&a!==n;){var h=a,y=h.alternate,N=h.stateNode;if(h=h.tag,y!==null&&y===n)break;h!==5&&h!==26&&h!==27||N===null||(y=N,i?(N=pl(a,s),N!=null&&f.unshift(Wl(a,N,y))):i||(N=pl(a,s),N!=null&&f.push(Wl(a,N,y)))),a=a.return}f.length!==0&&e.push({event:t,listeners:f})}var zh=/\r\n?/g,Lh=/\u0000|\uFFFD/g;function ad(e){return(typeof e=="string"?e:""+e).replace(zh,`
`).replace(Lh,"")}function nd(e,t){return t=ad(t),ad(e)===t}function ms(){}function Ue(e,t,a,n,i,s){switch(a){case"children":typeof n=="string"?t==="body"||t==="textarea"&&n===""||Mn(e,n):(typeof n=="number"||typeof n=="bigint")&&t!=="body"&&Mn(e,""+n);break;case"className":bi(e,"class",n);break;case"tabIndex":bi(e,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":bi(e,a,n);break;case"style":no(e,n,s);break;case"data":if(t!=="object"){bi(e,"data",n);break}case"src":case"href":if(n===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){e.removeAttribute(a);break}n=Ei(""+n),e.setAttribute(a,n);break;case"action":case"formAction":if(typeof n=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof s=="function"&&(a==="formAction"?(t!=="input"&&Ue(e,t,"name",i.name,i,null),Ue(e,t,"formEncType",i.formEncType,i,null),Ue(e,t,"formMethod",i.formMethod,i,null),Ue(e,t,"formTarget",i.formTarget,i,null)):(Ue(e,t,"encType",i.encType,i,null),Ue(e,t,"method",i.method,i,null),Ue(e,t,"target",i.target,i,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){e.removeAttribute(a);break}n=Ei(""+n),e.setAttribute(a,n);break;case"onClick":n!=null&&(e.onclick=ms);break;case"onScroll":n!=null&&Me("scroll",e);break;case"onScrollEnd":n!=null&&Me("scrollend",e);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(r(61));if(a=n.__html,a!=null){if(i.children!=null)throw Error(r(60));e.innerHTML=a}}break;case"multiple":e.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":e.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){e.removeAttribute("xlink:href");break}a=Ei(""+n),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(a,""+n):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":n===!0?e.setAttribute(a,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(a,n):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?e.setAttribute(a,n):e.removeAttribute(a);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?e.removeAttribute(a):e.setAttribute(a,n);break;case"popover":Me("beforetoggle",e),Me("toggle",e),vi(e,"popover",n);break;case"xlinkActuate":It(e,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":It(e,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":It(e,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":It(e,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":It(e,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":It(e,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":It(e,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":It(e,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":It(e,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":vi(e,"is",n);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=u0.get(a)||a,vi(e,a,n))}}function ku(e,t,a,n,i,s){switch(a){case"style":no(e,n,s);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(r(61));if(a=n.__html,a!=null){if(i.children!=null)throw Error(r(60));e.innerHTML=a}}break;case"children":typeof n=="string"?Mn(e,n):(typeof n=="number"||typeof n=="bigint")&&Mn(e,""+n);break;case"onScroll":n!=null&&Me("scroll",e);break;case"onScrollEnd":n!=null&&Me("scrollend",e);break;case"onClick":n!=null&&(e.onclick=ms);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Xr.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(i=a.endsWith("Capture"),t=a.slice(2,i?a.length-7:void 0),s=e[gt]||null,s=s!=null?s[a]:null,typeof s=="function"&&e.removeEventListener(t,s,i),typeof n=="function")){typeof s!="function"&&s!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,n,i);break e}a in e?e[a]=n:n===!0?e.setAttribute(a,""):vi(e,a,n)}}}function st(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Me("error",e),Me("load",e);var n=!1,i=!1,s;for(s in a)if(a.hasOwnProperty(s)){var f=a[s];if(f!=null)switch(s){case"src":n=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:Ue(e,t,s,f,a,null)}}i&&Ue(e,t,"srcSet",a.srcSet,a,null),n&&Ue(e,t,"src",a.src,a,null);return;case"input":Me("invalid",e);var h=s=f=i=null,y=null,N=null;for(n in a)if(a.hasOwnProperty(n)){var _=a[n];if(_!=null)switch(n){case"name":i=_;break;case"type":f=_;break;case"checked":y=_;break;case"defaultChecked":N=_;break;case"value":s=_;break;case"defaultValue":h=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(r(137,t));break;default:Ue(e,t,n,_,a,null)}}Ir(e,s,h,y,N,f,i,!1),Ci(e);return;case"select":Me("invalid",e),n=f=s=null;for(i in a)if(a.hasOwnProperty(i)&&(h=a[i],h!=null))switch(i){case"value":s=h;break;case"defaultValue":f=h;break;case"multiple":n=h;default:Ue(e,t,i,h,a,null)}t=s,a=f,e.multiple=!!n,t!=null?wn(e,!!n,t,!1):a!=null&&wn(e,!!n,a,!0);return;case"textarea":Me("invalid",e),s=i=n=null;for(f in a)if(a.hasOwnProperty(f)&&(h=a[f],h!=null))switch(f){case"value":n=h;break;case"defaultValue":i=h;break;case"children":s=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(r(91));break;default:Ue(e,t,f,h,a,null)}to(e,n,i,s),Ci(e);return;case"option":for(y in a)if(a.hasOwnProperty(y)&&(n=a[y],n!=null))switch(y){case"selected":e.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:Ue(e,t,y,n,a,null)}return;case"dialog":Me("beforetoggle",e),Me("toggle",e),Me("cancel",e),Me("close",e);break;case"iframe":case"object":Me("load",e);break;case"video":case"audio":for(n=0;n<Fl.length;n++)Me(Fl[n],e);break;case"image":Me("error",e),Me("load",e);break;case"details":Me("toggle",e);break;case"embed":case"source":case"link":Me("error",e),Me("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(N in a)if(a.hasOwnProperty(N)&&(n=a[N],n!=null))switch(N){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:Ue(e,t,N,n,a,null)}return;default:if(ec(t)){for(_ in a)a.hasOwnProperty(_)&&(n=a[_],n!==void 0&&ku(e,t,_,n,a,void 0));return}}for(h in a)a.hasOwnProperty(h)&&(n=a[h],n!=null&&Ue(e,t,h,n,a,null))}function Dh(e,t,a,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,s=null,f=null,h=null,y=null,N=null,_=null;for(z in a){var B=a[z];if(a.hasOwnProperty(z)&&B!=null)switch(z){case"checked":break;case"value":break;case"defaultValue":y=B;default:n.hasOwnProperty(z)||Ue(e,t,z,null,n,B)}}for(var x in n){var z=n[x];if(B=a[x],n.hasOwnProperty(x)&&(z!=null||B!=null))switch(x){case"type":s=z;break;case"name":i=z;break;case"checked":N=z;break;case"defaultChecked":_=z;break;case"value":f=z;break;case"defaultValue":h=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(r(137,t));break;default:z!==B&&Ue(e,t,x,z,n,B)}}Ps(e,f,h,y,N,_,s,i);return;case"select":z=f=h=x=null;for(s in a)if(y=a[s],a.hasOwnProperty(s)&&y!=null)switch(s){case"value":break;case"multiple":z=y;default:n.hasOwnProperty(s)||Ue(e,t,s,null,n,y)}for(i in n)if(s=n[i],y=a[i],n.hasOwnProperty(i)&&(s!=null||y!=null))switch(i){case"value":x=s;break;case"defaultValue":h=s;break;case"multiple":f=s;default:s!==y&&Ue(e,t,i,s,n,y)}t=h,a=f,n=z,x!=null?wn(e,!!a,x,!1):!!n!=!!a&&(t!=null?wn(e,!!a,t,!0):wn(e,!!a,a?[]:"",!1));return;case"textarea":z=x=null;for(h in a)if(i=a[h],a.hasOwnProperty(h)&&i!=null&&!n.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:Ue(e,t,h,null,n,i)}for(f in n)if(i=n[f],s=a[f],n.hasOwnProperty(f)&&(i!=null||s!=null))switch(f){case"value":x=i;break;case"defaultValue":z=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(r(91));break;default:i!==s&&Ue(e,t,f,i,n,s)}eo(e,x,z);return;case"option":for(var ye in a)if(x=a[ye],a.hasOwnProperty(ye)&&x!=null&&!n.hasOwnProperty(ye))switch(ye){case"selected":e.selected=!1;break;default:Ue(e,t,ye,null,n,x)}for(y in n)if(x=n[y],z=a[y],n.hasOwnProperty(y)&&x!==z&&(x!=null||z!=null))switch(y){case"selected":e.selected=x&&typeof x!="function"&&typeof x!="symbol";break;default:Ue(e,t,y,x,n,z)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var fe in a)x=a[fe],a.hasOwnProperty(fe)&&x!=null&&!n.hasOwnProperty(fe)&&Ue(e,t,fe,null,n,x);for(N in n)if(x=n[N],z=a[N],n.hasOwnProperty(N)&&x!==z&&(x!=null||z!=null))switch(N){case"children":case"dangerouslySetInnerHTML":if(x!=null)throw Error(r(137,t));break;default:Ue(e,t,N,x,n,z)}return;default:if(ec(t)){for(var He in a)x=a[He],a.hasOwnProperty(He)&&x!==void 0&&!n.hasOwnProperty(He)&&ku(e,t,He,void 0,n,x);for(_ in n)x=n[_],z=a[_],!n.hasOwnProperty(_)||x===z||x===void 0&&z===void 0||ku(e,t,_,x,n,z);return}}for(var T in a)x=a[T],a.hasOwnProperty(T)&&x!=null&&!n.hasOwnProperty(T)&&Ue(e,t,T,null,n,x);for(B in n)x=n[B],z=a[B],!n.hasOwnProperty(B)||x===z||x==null&&z==null||Ue(e,t,B,x,n,z)}var qu=null,Bu=null;function ps(e){return e.nodeType===9?e:e.ownerDocument}function ld(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function id(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Vu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Gu=null;function jh(){var e=window.event;return e&&e.type==="popstate"?e===Gu?!1:(Gu=e,!0):(Gu=null,!1)}var sd=typeof setTimeout=="function"?setTimeout:void 0,_h=typeof clearTimeout=="function"?clearTimeout:void 0,cd=typeof Promise=="function"?Promise:void 0,Uh=typeof queueMicrotask=="function"?queueMicrotask:typeof cd!="undefined"?function(e){return cd.resolve(null).then(e).catch(Hh)}:sd;function Hh(e){setTimeout(function(){throw e})}function Da(e){return e==="head"}function ud(e,t){var a=t,n=0,i=0;do{var s=a.nextSibling;if(e.removeChild(a),s&&s.nodeType===8)if(a=s.data,a==="/$"){if(0<n&&8>n){a=n;var f=e.ownerDocument;if(a&1&&Pl(f.documentElement),a&2&&Pl(f.body),a&4)for(a=f.head,Pl(a),f=a.firstChild;f;){var h=f.nextSibling,y=f.nodeName;f[hl]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=h}}if(i===0){e.removeChild(s),si(t);return}i--}else a==="$"||a==="$?"||a==="$!"?i++:n=a.charCodeAt(0)-48;else n=0;a=s}while(a);si(t)}function Yu(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Yu(a),Ks(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function kh(e,t,a,n){for(;e.nodeType===1;){var i=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!n&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(n){if(!e[hl])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(s=e.getAttribute("rel"),s==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(s!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(s=e.getAttribute("src"),(s!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&s&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var s=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===s)return e}else return e;if(e=Yt(e.nextSibling),e===null)break}return null}function qh(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Yt(e.nextSibling),e===null))return null;return e}function Qu(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Bh(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var n=function(){t(),a.removeEventListener("DOMContentLoaded",n)};a.addEventListener("DOMContentLoaded",n),e._reactRetry=n}}function Yt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Zu=null;function rd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function od(e,t,a){switch(t=ps(a),e){case"html":if(e=t.documentElement,!e)throw Error(r(452));return e;case"head":if(e=t.head,!e)throw Error(r(453));return e;case"body":if(e=t.body,!e)throw Error(r(454));return e;default:throw Error(r(451))}}function Pl(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ks(e)}var Bt=new Map,fd=new Set;function gs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ha=$.d;$.d={f:Vh,r:Gh,D:Yh,C:Qh,L:Zh,m:Xh,X:Kh,S:$h,M:Jh};function Vh(){var e=ha.f(),t=cs();return e||t}function Gh(e){var t=Sn(e);t!==null&&t.tag===5&&t.type==="form"?zf(t):ha.r(e)}var tl=typeof document=="undefined"?null:document;function dd(e,t,a){var n=tl;if(n&&typeof t=="string"&&t){var i=Dt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof a=="string"&&(i+='[crossorigin="'+a+'"]'),fd.has(i)||(fd.add(i),e={rel:e,crossOrigin:a,href:t},n.querySelector(i)===null&&(t=n.createElement("link"),st(t,"link",e),et(t),n.head.appendChild(t)))}}function Yh(e){ha.D(e),dd("dns-prefetch",e,null)}function Qh(e,t){ha.C(e,t),dd("preconnect",e,t)}function Zh(e,t,a){ha.L(e,t,a);var n=tl;if(n&&e&&t){var i='link[rel="preload"][as="'+Dt(t)+'"]';t==="image"&&a&&a.imageSrcSet?(i+='[imagesrcset="'+Dt(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(i+='[imagesizes="'+Dt(a.imageSizes)+'"]')):i+='[href="'+Dt(e)+'"]';var s=i;switch(t){case"style":s=al(e);break;case"script":s=nl(e)}Bt.has(s)||(e=S({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Bt.set(s,e),n.querySelector(i)!==null||t==="style"&&n.querySelector(Il(s))||t==="script"&&n.querySelector(ei(s))||(t=n.createElement("link"),st(t,"link",e),et(t),n.head.appendChild(t)))}}function Xh(e,t){ha.m(e,t);var a=tl;if(a&&e){var n=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Dt(n)+'"][href="'+Dt(e)+'"]',s=i;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=nl(e)}if(!Bt.has(s)&&(e=S({rel:"modulepreload",href:e},t),Bt.set(s,e),a.querySelector(i)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(ei(s)))return}n=a.createElement("link"),st(n,"link",e),et(n),a.head.appendChild(n)}}}function $h(e,t,a){ha.S(e,t,a);var n=tl;if(n&&e){var i=En(n).hoistableStyles,s=al(e);t=t||"default";var f=i.get(s);if(!f){var h={loading:0,preload:null};if(f=n.querySelector(Il(s)))h.loading=5;else{e=S({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Bt.get(s))&&Xu(e,a);var y=f=n.createElement("link");et(y),st(y,"link",e),y._p=new Promise(function(N,_){y.onload=N,y.onerror=_}),y.addEventListener("load",function(){h.loading|=1}),y.addEventListener("error",function(){h.loading|=2}),h.loading|=4,ys(f,t,n)}f={type:"stylesheet",instance:f,count:1,state:h},i.set(s,f)}}}function Kh(e,t){ha.X(e,t);var a=tl;if(a&&e){var n=En(a).hoistableScripts,i=nl(e),s=n.get(i);s||(s=a.querySelector(ei(i)),s||(e=S({src:e,async:!0},t),(t=Bt.get(i))&&$u(e,t),s=a.createElement("script"),et(s),st(s,"link",e),a.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},n.set(i,s))}}function Jh(e,t){ha.M(e,t);var a=tl;if(a&&e){var n=En(a).hoistableScripts,i=nl(e),s=n.get(i);s||(s=a.querySelector(ei(i)),s||(e=S({src:e,async:!0,type:"module"},t),(t=Bt.get(i))&&$u(e,t),s=a.createElement("script"),et(s),st(s,"link",e),a.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},n.set(i,s))}}function hd(e,t,a,n){var i=(i=ve.current)?gs(i):null;if(!i)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=al(a.href),a=En(i).hoistableStyles,n=a.get(t),n||(n={type:"style",instance:null,count:0,state:null},a.set(t,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=al(a.href);var s=En(i).hoistableStyles,f=s.get(e);if(f||(i=i.ownerDocument||i,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},s.set(e,f),(s=i.querySelector(Il(e)))&&!s._p&&(f.instance=s,f.state.loading=5),Bt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Bt.set(e,a),s||Fh(i,e,a,f.state))),t&&n===null)throw Error(r(528,""));return f}if(t&&n!==null)throw Error(r(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=nl(a),a=En(i).hoistableScripts,n=a.get(t),n||(n={type:"script",instance:null,count:0,state:null},a.set(t,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function al(e){return'href="'+Dt(e)+'"'}function Il(e){return'link[rel="stylesheet"]['+e+"]"}function md(e){return S({},e,{"data-precedence":e.precedence,precedence:null})}function Fh(e,t,a,n){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?n.loading=1:(t=e.createElement("link"),n.preload=t,t.addEventListener("load",function(){return n.loading|=1}),t.addEventListener("error",function(){return n.loading|=2}),st(t,"link",a),et(t),e.head.appendChild(t))}function nl(e){return'[src="'+Dt(e)+'"]'}function ei(e){return"script[async]"+e}function pd(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var n=e.querySelector('style[data-href~="'+Dt(a.href)+'"]');if(n)return t.instance=n,et(n),n;var i=S({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return n=(e.ownerDocument||e).createElement("style"),et(n),st(n,"style",i),ys(n,a.precedence,e),t.instance=n;case"stylesheet":i=al(a.href);var s=e.querySelector(Il(i));if(s)return t.state.loading|=4,t.instance=s,et(s),s;n=md(a),(i=Bt.get(i))&&Xu(n,i),s=(e.ownerDocument||e).createElement("link"),et(s);var f=s;return f._p=new Promise(function(h,y){f.onload=h,f.onerror=y}),st(s,"link",n),t.state.loading|=4,ys(s,a.precedence,e),t.instance=s;case"script":return s=nl(a.src),(i=e.querySelector(ei(s)))?(t.instance=i,et(i),i):(n=a,(i=Bt.get(s))&&(n=S({},a),$u(n,i)),e=e.ownerDocument||e,i=e.createElement("script"),et(i),st(i,"link",n),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(r(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(n=t.instance,t.state.loading|=4,ys(n,a.precedence,e));return t.instance}function ys(e,t,a){for(var n=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=n.length?n[n.length-1]:null,s=i,f=0;f<n.length;f++){var h=n[f];if(h.dataset.precedence===t)s=h;else if(s!==i)break}s?s.parentNode.insertBefore(e,s.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Xu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function $u(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var vs=null;function gd(e,t,a){if(vs===null){var n=new Map,i=vs=new Map;i.set(a,n)}else i=vs,n=i.get(a),n||(n=new Map,i.set(a,n));if(n.has(e))return n;for(n.set(e,null),a=a.getElementsByTagName(e),i=0;i<a.length;i++){var s=a[i];if(!(s[hl]||s[ot]||e==="link"&&s.getAttribute("rel")==="stylesheet")&&s.namespaceURI!=="http://www.w3.org/2000/svg"){var f=s.getAttribute(t)||"";f=e+f;var h=n.get(f);h?h.push(s):n.set(f,[s])}}return n}function yd(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Wh(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function vd(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var ti=null;function Ph(){}function Ih(e,t,a){if(ti===null)throw Error(r(475));var n=ti;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=al(a.href),s=e.querySelector(Il(i));if(s){e=s._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(n.count++,n=bs.bind(n),e.then(n,n)),t.state.loading|=4,t.instance=s,et(s);return}s=e.ownerDocument||e,a=md(a),(i=Bt.get(i))&&Xu(a,i),s=s.createElement("link"),et(s);var f=s;f._p=new Promise(function(h,y){f.onload=h,f.onerror=y}),st(s,"link",a),t.instance=s}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(n.count++,t=bs.bind(n),e.addEventListener("load",t),e.addEventListener("error",t))}}function em(){if(ti===null)throw Error(r(475));var e=ti;return e.stylesheets&&e.count===0&&Ku(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Ku(e,e.stylesheets),e.unsuspend){var n=e.unsuspend;e.unsuspend=null,n()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function bs(){if(this.count--,this.count===0){if(this.stylesheets)Ku(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Cs=null;function Ku(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Cs=new Map,t.forEach(tm,e),Cs=null,bs.call(e))}function tm(e,t){if(!(t.state.loading&4)){var a=Cs.get(e);if(a)var n=a.get(null);else{a=new Map,Cs.set(e,a);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<i.length;s++){var f=i[s];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),n=f)}n&&a.set(null,n)}i=t.instance,f=i.getAttribute("data-precedence"),s=a.get(f)||n,s===n&&a.set(null,i),a.set(f,i),this.count++,n=bs.bind(this),i.addEventListener("load",n),i.addEventListener("error",n),s?s.parentNode.insertBefore(i,s.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var ai={$$typeof:P,Provider:null,Consumer:null,_currentValue:le,_currentValue2:le,_threadCount:0};function am(e,t,a,n,i,s,f,h){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Qs(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Qs(0),this.hiddenUpdates=Qs(null),this.identifierPrefix=n,this.onUncaughtError=i,this.onCaughtError=s,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function bd(e,t,a,n,i,s,f,h,y,N,_,B){return e=new am(e,t,a,f,h,y,N,B),t=1,s===!0&&(t|=24),s=Mt(3,null,null,t),e.current=s,s.stateNode=e,t=xc(),t.refCount++,e.pooledCache=t,t.refCount++,s.memoizedState={element:n,isDehydrated:a,cache:t},Dc(s),e}function Cd(e){return e?(e=jn,e):jn}function Sd(e,t,a,n,i,s){i=Cd(i),n.context===null?n.context=i:n.pendingContext=i,n=Ca(t),n.payload={element:a},s=s===void 0?null:s,s!==null&&(n.callback=s),a=Sa(e,n,t),a!==null&&(zt(a,e,t),Ll(a,e,t))}function Ed(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function Ju(e,t){Ed(e,t),(e=e.alternate)&&Ed(e,t)}function Ad(e){if(e.tag===13){var t=Dn(e,67108864);t!==null&&zt(t,e,67108864),Ju(e,67108864)}}var Ss=!0;function nm(e,t,a,n){var i=L.T;L.T=null;var s=$.p;try{$.p=2,Fu(e,t,a,n)}finally{$.p=s,L.T=i}}function lm(e,t,a,n){var i=L.T;L.T=null;var s=$.p;try{$.p=8,Fu(e,t,a,n)}finally{$.p=s,L.T=i}}function Fu(e,t,a,n){if(Ss){var i=Wu(n);if(i===null)Hu(e,t,n,Es,a),wd(e,n);else if(sm(i,e,t,a,n))n.stopPropagation();else if(wd(e,n),t&4&&-1<im.indexOf(e)){for(;i!==null;){var s=Sn(i);if(s!==null)switch(s.tag){case 3:if(s=s.stateNode,s.current.memoizedState.isDehydrated){var f=Ba(s.pendingLanes);if(f!==0){var h=s;for(h.pendingLanes|=2,h.entangledLanes|=2;f;){var y=1<<31-Tt(f);h.entanglements[1]|=y,f&=~y}Ft(s),(De&6)===0&&(is=Zt()+500,Jl(0))}}break;case 13:h=Dn(s,2),h!==null&&zt(h,s,2),cs(),Ju(s,2)}if(s=Wu(n),s===null&&Hu(e,t,n,Es,a),s===i)break;i=s}i!==null&&n.stopPropagation()}else Hu(e,t,n,null,a)}}function Wu(e){return e=ac(e),Pu(e)}var Es=null;function Pu(e){if(Es=null,e=Cn(e),e!==null){var t=d(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=m(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Es=e,null}function Td(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Q2()){case Ur:return 2;case Hr:return 8;case mi:case Z2:return 32;case kr:return 268435456;default:return 32}default:return 32}}var Iu=!1,ja=null,_a=null,Ua=null,ni=new Map,li=new Map,Ha=[],im="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wd(e,t){switch(e){case"focusin":case"focusout":ja=null;break;case"dragenter":case"dragleave":_a=null;break;case"mouseover":case"mouseout":Ua=null;break;case"pointerover":case"pointerout":ni.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":li.delete(t.pointerId)}}function ii(e,t,a,n,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:a,eventSystemFlags:n,nativeEvent:s,targetContainers:[i]},t!==null&&(t=Sn(t),t!==null&&Ad(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function sm(e,t,a,n,i){switch(t){case"focusin":return ja=ii(ja,e,t,a,n,i),!0;case"dragenter":return _a=ii(_a,e,t,a,n,i),!0;case"mouseover":return Ua=ii(Ua,e,t,a,n,i),!0;case"pointerover":var s=i.pointerId;return ni.set(s,ii(ni.get(s)||null,e,t,a,n,i)),!0;case"gotpointercapture":return s=i.pointerId,li.set(s,ii(li.get(s)||null,e,t,a,n,i)),!0}return!1}function Md(e){var t=Cn(e.target);if(t!==null){var a=d(t);if(a!==null){if(t=a.tag,t===13){if(t=m(a),t!==null){e.blockedOn=t,I2(e.priority,function(){if(a.tag===13){var n=Ot();n=Zs(n);var i=Dn(a,n);i!==null&&zt(i,a,n),Ju(a,n)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function As(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Wu(e.nativeEvent);if(a===null){a=e.nativeEvent;var n=new a.constructor(a.type,a);tc=n,a.target.dispatchEvent(n),tc=null}else return t=Sn(a),t!==null&&Ad(t),e.blockedOn=a,!1;t.shift()}return!0}function Nd(e,t,a){As(e)&&a.delete(t)}function cm(){Iu=!1,ja!==null&&As(ja)&&(ja=null),_a!==null&&As(_a)&&(_a=null),Ua!==null&&As(Ua)&&(Ua=null),ni.forEach(Nd),li.forEach(Nd)}function Ts(e,t){e.blockedOn===t&&(e.blockedOn=null,Iu||(Iu=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,cm)))}var ws=null;function Rd(e){ws!==e&&(ws=e,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){ws===e&&(ws=null);for(var t=0;t<e.length;t+=3){var a=e[t],n=e[t+1],i=e[t+2];if(typeof n!="function"){if(Pu(n||a)===null)continue;break}var s=Sn(a);s!==null&&(e.splice(t,3),t-=3,Pc(s,{pending:!0,data:i,method:a.method,action:n},n,i))}}))}function si(e){function t(y){return Ts(y,e)}ja!==null&&Ts(ja,e),_a!==null&&Ts(_a,e),Ua!==null&&Ts(Ua,e),ni.forEach(t),li.forEach(t);for(var a=0;a<Ha.length;a++){var n=Ha[a];n.blockedOn===e&&(n.blockedOn=null)}for(;0<Ha.length&&(a=Ha[0],a.blockedOn===null);)Md(a),a.blockedOn===null&&Ha.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(n=0;n<a.length;n+=3){var i=a[n],s=a[n+1],f=i[gt]||null;if(typeof s=="function")f||Rd(a);else if(f){var h=null;if(s&&s.hasAttribute("formAction")){if(i=s,f=s[gt]||null)h=f.formAction;else if(Pu(i)!==null)continue}else h=f.action;typeof h=="function"?a[n+1]=h:(a.splice(n,3),n-=3),Rd(a)}}}function er(e){this._internalRoot=e}Ms.prototype.render=er.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(r(409));var a=t.current,n=Ot();Sd(a,n,e,t,null,null)},Ms.prototype.unmount=er.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Sd(e.current,2,null,e,null,null),cs(),t[bn]=null}};function Ms(e){this._internalRoot=e}Ms.prototype.unstable_scheduleHydration=function(e){if(e){var t=Yr();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Ha.length&&t!==0&&t<Ha[a].priority;a++);Ha.splice(a,0,e),a===0&&Md(e)}};var xd=l.version;if(xd!=="19.1.0")throw Error(r(527,xd,"19.1.0"));$.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=b(t),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var um={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:L,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"){var Ns=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ns.isDisabled&&Ns.supportsFiber)try{ol=Ns.inject(um),At=Ns}catch(e){}}return ui.createRoot=function(e,t){if(!o(e))throw Error(r(299));var a=!1,n="",i=Zf,s=Xf,f=$f,h=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(s=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(h=t.unstable_transitionCallbacks)),t=bd(e,1,!1,null,null,a,n,i,s,f,h,null),e[bn]=t.current,Uu(e),new er(t)},ui.hydrateRoot=function(e,t,a){if(!o(e))throw Error(r(299));var n=!1,i="",s=Zf,f=Xf,h=$f,y=null,N=null;return a!=null&&(a.unstable_strictMode===!0&&(n=!0),a.identifierPrefix!==void 0&&(i=a.identifierPrefix),a.onUncaughtError!==void 0&&(s=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(h=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(y=a.unstable_transitionCallbacks),a.formState!==void 0&&(N=a.formState)),t=bd(e,1,!0,t,a!=null?a:null,n,i,s,f,h,y,N),t.context=Cd(null),a=t.current,n=Ot(),n=Zs(n),i=Ca(n),i.callback=null,Sa(a,i,n),a=n,t.current.lanes=a,dl(t,a),Ft(t),e[bn]=t.current,Uu(e),new Ms(t)},ui.version="19.1.0",ui}var Vd;function Mm(){if(Vd)return ir.exports;Vd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(l){console.error(l)}}return c(),ir.exports=wm(),ir.exports}var Nm=Mm(),C=Or();const X=ym(C);function r2(c,l){return function(){return c.apply(l,arguments)}}const{toString:Rm}=Object.prototype,{getPrototypeOf:zr}=Object,{iterator:js,toStringTag:o2}=Symbol,_s=(c=>l=>{const u=Rm.call(l);return c[u]||(c[u]=u.slice(8,-1).toLowerCase())})(Object.create(null)),Qt=c=>(c=c.toLowerCase(),l=>_s(l)===c),Us=c=>l=>typeof l===c,{isArray:cl}=Array,fi=Us("undefined");function xm(c){return c!==null&&!fi(c)&&c.constructor!==null&&!fi(c.constructor)&&St(c.constructor.isBuffer)&&c.constructor.isBuffer(c)}const f2=Qt("ArrayBuffer");function Om(c){let l;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?l=ArrayBuffer.isView(c):l=c&&c.buffer&&f2(c.buffer),l}const zm=Us("string"),St=Us("function"),d2=Us("number"),Hs=c=>c!==null&&typeof c=="object",Lm=c=>c===!0||c===!1,xs=c=>{if(_s(c)!=="object")return!1;const l=zr(c);return(l===null||l===Object.prototype||Object.getPrototypeOf(l)===null)&&!(o2 in c)&&!(js in c)},Dm=Qt("Date"),jm=Qt("File"),_m=Qt("Blob"),Um=Qt("FileList"),Hm=c=>Hs(c)&&St(c.pipe),km=c=>{let l;return c&&(typeof FormData=="function"&&c instanceof FormData||St(c.append)&&((l=_s(c))==="formdata"||l==="object"&&St(c.toString)&&c.toString()==="[object FormData]"))},qm=Qt("URLSearchParams"),[Bm,Vm,Gm,Ym]=["ReadableStream","Request","Response","Headers"].map(Qt),Qm=c=>c.trim?c.trim():c.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function di(c,l,{allOwnKeys:u=!1}={}){if(c===null||typeof c=="undefined")return;let r,o;if(typeof c!="object"&&(c=[c]),cl(c))for(r=0,o=c.length;r<o;r++)l.call(null,c[r],r,c);else{const d=u?Object.getOwnPropertyNames(c):Object.keys(c),m=d.length;let g;for(r=0;r<m;r++)g=d[r],l.call(null,c[g],g,c)}}function h2(c,l){l=l.toLowerCase();const u=Object.keys(c);let r=u.length,o;for(;r-- >0;)if(o=u[r],l===o.toLowerCase())return o;return null}const gn=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:global,m2=c=>!fi(c)&&c!==gn;function pr(){const{caseless:c}=m2(this)&&this||{},l={},u=(r,o)=>{const d=c&&h2(l,o)||o;xs(l[d])&&xs(r)?l[d]=pr(l[d],r):xs(r)?l[d]=pr({},r):cl(r)?l[d]=r.slice():l[d]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&di(arguments[r],u);return l}const Zm=(c,l,u,{allOwnKeys:r}={})=>(di(l,(o,d)=>{u&&St(o)?c[d]=r2(o,u):c[d]=o},{allOwnKeys:r}),c),Xm=c=>(c.charCodeAt(0)===65279&&(c=c.slice(1)),c),$m=(c,l,u,r)=>{c.prototype=Object.create(l.prototype,r),c.prototype.constructor=c,Object.defineProperty(c,"super",{value:l.prototype}),u&&Object.assign(c.prototype,u)},Km=(c,l,u,r)=>{let o,d,m;const g={};if(l=l||{},c==null)return l;do{for(o=Object.getOwnPropertyNames(c),d=o.length;d-- >0;)m=o[d],(!r||r(m,c,l))&&!g[m]&&(l[m]=c[m],g[m]=!0);c=u!==!1&&zr(c)}while(c&&(!u||u(c,l))&&c!==Object.prototype);return l},Jm=(c,l,u)=>{c=String(c),(u===void 0||u>c.length)&&(u=c.length),u-=l.length;const r=c.indexOf(l,u);return r!==-1&&r===u},Fm=c=>{if(!c)return null;if(cl(c))return c;let l=c.length;if(!d2(l))return null;const u=new Array(l);for(;l-- >0;)u[l]=c[l];return u},Wm=(c=>l=>c&&l instanceof c)(typeof Uint8Array!="undefined"&&zr(Uint8Array)),Pm=(c,l)=>{const r=(c&&c[js]).call(c);let o;for(;(o=r.next())&&!o.done;){const d=o.value;l.call(c,d[0],d[1])}},Im=(c,l)=>{let u;const r=[];for(;(u=c.exec(l))!==null;)r.push(u);return r},ep=Qt("HTMLFormElement"),tp=c=>c.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(u,r,o){return r.toUpperCase()+o}),Gd=(({hasOwnProperty:c})=>(l,u)=>c.call(l,u))(Object.prototype),ap=Qt("RegExp"),p2=(c,l)=>{const u=Object.getOwnPropertyDescriptors(c),r={};di(u,(o,d)=>{let m;(m=l(o,d,c))!==!1&&(r[d]=m||o)}),Object.defineProperties(c,r)},np=c=>{p2(c,(l,u)=>{if(St(c)&&["arguments","caller","callee"].indexOf(u)!==-1)return!1;const r=c[u];if(St(r)){if(l.enumerable=!1,"writable"in l){l.writable=!1;return}l.set||(l.set=()=>{throw Error("Can not rewrite read-only method '"+u+"'")})}})},lp=(c,l)=>{const u={},r=o=>{o.forEach(d=>{u[d]=!0})};return cl(c)?r(c):r(String(c).split(l)),u},ip=()=>{},sp=(c,l)=>c!=null&&Number.isFinite(c=+c)?c:l;function cp(c){return!!(c&&St(c.append)&&c[o2]==="FormData"&&c[js])}const up=c=>{const l=new Array(10),u=(r,o)=>{if(Hs(r)){if(l.indexOf(r)>=0)return;if(!("toJSON"in r)){l[o]=r;const d=cl(r)?[]:{};return di(r,(m,g)=>{const b=u(m,o+1);!fi(b)&&(d[g]=b)}),l[o]=void 0,d}}return r};return u(c,0)},rp=Qt("AsyncFunction"),op=c=>c&&(Hs(c)||St(c))&&St(c.then)&&St(c.catch),g2=((c,l)=>c?setImmediate:l?((u,r)=>(gn.addEventListener("message",({source:o,data:d})=>{o===gn&&d===u&&r.length&&r.shift()()},!1),o=>{r.push(o),gn.postMessage(u,"*")}))(`axios@${Math.random()}`,[]):u=>setTimeout(u))(typeof setImmediate=="function",St(gn.postMessage)),fp=typeof queueMicrotask!="undefined"?queueMicrotask.bind(gn):typeof process!="undefined"&&process.nextTick||g2,dp=c=>c!=null&&St(c[js]),D={isArray:cl,isArrayBuffer:f2,isBuffer:xm,isFormData:km,isArrayBufferView:Om,isString:zm,isNumber:d2,isBoolean:Lm,isObject:Hs,isPlainObject:xs,isReadableStream:Bm,isRequest:Vm,isResponse:Gm,isHeaders:Ym,isUndefined:fi,isDate:Dm,isFile:jm,isBlob:_m,isRegExp:ap,isFunction:St,isStream:Hm,isURLSearchParams:qm,isTypedArray:Wm,isFileList:Um,forEach:di,merge:pr,extend:Zm,trim:Qm,stripBOM:Xm,inherits:$m,toFlatObject:Km,kindOf:_s,kindOfTest:Qt,endsWith:Jm,toArray:Fm,forEachEntry:Pm,matchAll:Im,isHTMLForm:ep,hasOwnProperty:Gd,hasOwnProp:Gd,reduceDescriptors:p2,freezeMethods:np,toObjectSet:lp,toCamelCase:tp,noop:ip,toFiniteNumber:sp,findKey:h2,global:gn,isContextDefined:m2,isSpecCompliantForm:cp,toJSONObject:up,isAsyncFn:rp,isThenable:op,setImmediate:g2,asap:fp,isIterable:dp};function be(c,l,u,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=c,this.name="AxiosError",l&&(this.code=l),u&&(this.config=u),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}D.inherits(be,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:D.toJSONObject(this.config),code:this.code,status:this.status}}});const y2=be.prototype,v2={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(c=>{v2[c]={value:c}});Object.defineProperties(be,v2);Object.defineProperty(y2,"isAxiosError",{value:!0});be.from=(c,l,u,r,o,d)=>{const m=Object.create(y2);return D.toFlatObject(c,m,function(b){return b!==Error.prototype},g=>g!=="isAxiosError"),be.call(m,c.message,l,u,r,o),m.cause=c,m.name=c.name,d&&Object.assign(m,d),m};const hp=null;function gr(c){return D.isPlainObject(c)||D.isArray(c)}function b2(c){return D.endsWith(c,"[]")?c.slice(0,-2):c}function Yd(c,l,u){return c?c.concat(l).map(function(o,d){return o=b2(o),!u&&d?"["+o+"]":o}).join(u?".":""):l}function mp(c){return D.isArray(c)&&!c.some(gr)}const pp=D.toFlatObject(D,{},null,function(l){return/^is[A-Z]/.test(l)});function ks(c,l,u){if(!D.isObject(c))throw new TypeError("target must be an object");l=l||new FormData,u=D.toFlatObject(u,{metaTokens:!0,dots:!1,indexes:!1},!1,function(V,k){return!D.isUndefined(k[V])});const r=u.metaTokens,o=u.visitor||S,d=u.dots,m=u.indexes,b=(u.Blob||typeof Blob!="undefined"&&Blob)&&D.isSpecCompliantForm(l);if(!D.isFunction(o))throw new TypeError("visitor must be a function");function p(j){if(j===null)return"";if(D.isDate(j))return j.toISOString();if(D.isBoolean(j))return j.toString();if(!b&&D.isBlob(j))throw new be("Blob is not supported. Use a Buffer instead.");return D.isArrayBuffer(j)||D.isTypedArray(j)?b&&typeof Blob=="function"?new Blob([j]):Buffer.from(j):j}function S(j,V,k){let ee=j;if(j&&!k&&typeof j=="object"){if(D.endsWith(V,"{}"))V=r?V:V.slice(0,-2),j=JSON.stringify(j);else if(D.isArray(j)&&mp(j)||(D.isFileList(j)||D.endsWith(V,"[]"))&&(ee=D.toArray(j)))return V=b2(V),ee.forEach(function(P,Q){!(D.isUndefined(P)||P===null)&&l.append(m===!0?Yd([V],Q,d):m===null?V:V+"[]",p(P))}),!1}return gr(j)?!0:(l.append(Yd(k,V,d),p(j)),!1)}const R=[],H=Object.assign(pp,{defaultVisitor:S,convertValue:p,isVisitable:gr});function Z(j,V){if(!D.isUndefined(j)){if(R.indexOf(j)!==-1)throw Error("Circular reference detected in "+V.join("."));R.push(j),D.forEach(j,function(ee,ue){(!(D.isUndefined(ee)||ee===null)&&o.call(l,ee,D.isString(ue)?ue.trim():ue,V,H))===!0&&Z(ee,V?V.concat(ue):[ue])}),R.pop()}}if(!D.isObject(c))throw new TypeError("data must be an object");return Z(c),l}function Qd(c){const l={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(c).replace(/[!'()~]|%20|%00/g,function(r){return l[r]})}function Lr(c,l){this._pairs=[],c&&ks(c,this,l)}const C2=Lr.prototype;C2.append=function(l,u){this._pairs.push([l,u])};C2.toString=function(l){const u=l?function(r){return l.call(this,r,Qd)}:Qd;return this._pairs.map(function(o){return u(o[0])+"="+u(o[1])},"").join("&")};function gp(c){return encodeURIComponent(c).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function S2(c,l,u){if(!l)return c;const r=u&&u.encode||gp;D.isFunction(u)&&(u={serialize:u});const o=u&&u.serialize;let d;if(o?d=o(l,u):d=D.isURLSearchParams(l)?l.toString():new Lr(l,u).toString(r),d){const m=c.indexOf("#");m!==-1&&(c=c.slice(0,m)),c+=(c.indexOf("?")===-1?"?":"&")+d}return c}class Zd{constructor(){this.handlers=[]}use(l,u,r){return this.handlers.push({fulfilled:l,rejected:u,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(l){this.handlers[l]&&(this.handlers[l]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(l){D.forEach(this.handlers,function(r){r!==null&&l(r)})}}const E2={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},yp=typeof URLSearchParams!="undefined"?URLSearchParams:Lr,vp=typeof FormData!="undefined"?FormData:null,bp=typeof Blob!="undefined"?Blob:null,Cp={isBrowser:!0,classes:{URLSearchParams:yp,FormData:vp,Blob:bp},protocols:["http","https","file","blob","url","data"]},Dr=typeof window!="undefined"&&typeof document!="undefined",yr=typeof navigator=="object"&&navigator||void 0,Sp=Dr&&(!yr||["ReactNative","NativeScript","NS"].indexOf(yr.product)<0),Ep=typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ap=Dr&&window.location.href||"http://localhost",Tp=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Dr,hasStandardBrowserEnv:Sp,hasStandardBrowserWebWorkerEnv:Ep,navigator:yr,origin:Ap},Symbol.toStringTag,{value:"Module"})),ht=ne(ne({},Tp),Cp);function wp(c,l){return ks(c,new ht.classes.URLSearchParams,Object.assign({visitor:function(u,r,o,d){return ht.isNode&&D.isBuffer(u)?(this.append(r,u.toString("base64")),!1):d.defaultVisitor.apply(this,arguments)}},l))}function Mp(c){return D.matchAll(/\w+|\[(\w*)]/g,c).map(l=>l[0]==="[]"?"":l[1]||l[0])}function Np(c){const l={},u=Object.keys(c);let r;const o=u.length;let d;for(r=0;r<o;r++)d=u[r],l[d]=c[d];return l}function A2(c){function l(u,r,o,d){let m=u[d++];if(m==="__proto__")return!0;const g=Number.isFinite(+m),b=d>=u.length;return m=!m&&D.isArray(o)?o.length:m,b?(D.hasOwnProp(o,m)?o[m]=[o[m],r]:o[m]=r,!g):((!o[m]||!D.isObject(o[m]))&&(o[m]=[]),l(u,r,o[m],d)&&D.isArray(o[m])&&(o[m]=Np(o[m])),!g)}if(D.isFormData(c)&&D.isFunction(c.entries)){const u={};return D.forEachEntry(c,(r,o)=>{l(Mp(r),o,u,0)}),u}return null}function Rp(c,l,u){if(D.isString(c))try{return(l||JSON.parse)(c),D.trim(c)}catch(r){if(r.name!=="SyntaxError")throw r}return(u||JSON.stringify)(c)}const hi={transitional:E2,adapter:["xhr","http","fetch"],transformRequest:[function(l,u){const r=u.getContentType()||"",o=r.indexOf("application/json")>-1,d=D.isObject(l);if(d&&D.isHTMLForm(l)&&(l=new FormData(l)),D.isFormData(l))return o?JSON.stringify(A2(l)):l;if(D.isArrayBuffer(l)||D.isBuffer(l)||D.isStream(l)||D.isFile(l)||D.isBlob(l)||D.isReadableStream(l))return l;if(D.isArrayBufferView(l))return l.buffer;if(D.isURLSearchParams(l))return u.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),l.toString();let g;if(d){if(r.indexOf("application/x-www-form-urlencoded")>-1)return wp(l,this.formSerializer).toString();if((g=D.isFileList(l))||r.indexOf("multipart/form-data")>-1){const b=this.env&&this.env.FormData;return ks(g?{"files[]":l}:l,b&&new b,this.formSerializer)}}return d||o?(u.setContentType("application/json",!1),Rp(l)):l}],transformResponse:[function(l){const u=this.transitional||hi.transitional,r=u&&u.forcedJSONParsing,o=this.responseType==="json";if(D.isResponse(l)||D.isReadableStream(l))return l;if(l&&D.isString(l)&&(r&&!this.responseType||o)){const m=!(u&&u.silentJSONParsing)&&o;try{return JSON.parse(l)}catch(g){if(m)throw g.name==="SyntaxError"?be.from(g,be.ERR_BAD_RESPONSE,this,null,this.response):g}}return l}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ht.classes.FormData,Blob:ht.classes.Blob},validateStatus:function(l){return l>=200&&l<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};D.forEach(["delete","get","head","post","put","patch"],c=>{hi.headers[c]={}});const xp=D.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Op=c=>{const l={};let u,r,o;return c&&c.split(`
`).forEach(function(m){o=m.indexOf(":"),u=m.substring(0,o).trim().toLowerCase(),r=m.substring(o+1).trim(),!(!u||l[u]&&xp[u])&&(u==="set-cookie"?l[u]?l[u].push(r):l[u]=[r]:l[u]=l[u]?l[u]+", "+r:r)}),l},Xd=Symbol("internals");function ri(c){return c&&String(c).trim().toLowerCase()}function Os(c){return c===!1||c==null?c:D.isArray(c)?c.map(Os):String(c)}function zp(c){const l=Object.create(null),u=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=u.exec(c);)l[r[1]]=r[2];return l}const Lp=c=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(c.trim());function or(c,l,u,r,o){if(D.isFunction(r))return r.call(this,l,u);if(o&&(l=u),!!D.isString(l)){if(D.isString(r))return l.indexOf(r)!==-1;if(D.isRegExp(r))return r.test(l)}}function Dp(c){return c.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(l,u,r)=>u.toUpperCase()+r)}function jp(c,l){const u=D.toCamelCase(" "+l);["get","set","has"].forEach(r=>{Object.defineProperty(c,r+u,{value:function(o,d,m){return this[r].call(this,l,o,d,m)},configurable:!0})})}let Et=class{constructor(l){l&&this.set(l)}set(l,u,r){const o=this;function d(g,b,p){const S=ri(b);if(!S)throw new Error("header name must be a non-empty string");const R=D.findKey(o,S);(!R||o[R]===void 0||p===!0||p===void 0&&o[R]!==!1)&&(o[R||b]=Os(g))}const m=(g,b)=>D.forEach(g,(p,S)=>d(p,S,b));if(D.isPlainObject(l)||l instanceof this.constructor)m(l,u);else if(D.isString(l)&&(l=l.trim())&&!Lp(l))m(Op(l),u);else if(D.isObject(l)&&D.isIterable(l)){let g={},b,p;for(const S of l){if(!D.isArray(S))throw TypeError("Object iterator must return a key-value pair");g[p=S[0]]=(b=g[p])?D.isArray(b)?[...b,S[1]]:[b,S[1]]:S[1]}m(g,u)}else l!=null&&d(u,l,r);return this}get(l,u){if(l=ri(l),l){const r=D.findKey(this,l);if(r){const o=this[r];if(!u)return o;if(u===!0)return zp(o);if(D.isFunction(u))return u.call(this,o,r);if(D.isRegExp(u))return u.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(l,u){if(l=ri(l),l){const r=D.findKey(this,l);return!!(r&&this[r]!==void 0&&(!u||or(this,this[r],r,u)))}return!1}delete(l,u){const r=this;let o=!1;function d(m){if(m=ri(m),m){const g=D.findKey(r,m);g&&(!u||or(r,r[g],g,u))&&(delete r[g],o=!0)}}return D.isArray(l)?l.forEach(d):d(l),o}clear(l){const u=Object.keys(this);let r=u.length,o=!1;for(;r--;){const d=u[r];(!l||or(this,this[d],d,l,!0))&&(delete this[d],o=!0)}return o}normalize(l){const u=this,r={};return D.forEach(this,(o,d)=>{const m=D.findKey(r,d);if(m){u[m]=Os(o),delete u[d];return}const g=l?Dp(d):String(d).trim();g!==d&&delete u[d],u[g]=Os(o),r[g]=!0}),this}concat(...l){return this.constructor.concat(this,...l)}toJSON(l){const u=Object.create(null);return D.forEach(this,(r,o)=>{r!=null&&r!==!1&&(u[o]=l&&D.isArray(r)?r.join(", "):r)}),u}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([l,u])=>l+": "+u).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(l){return l instanceof this?l:new this(l)}static concat(l,...u){const r=new this(l);return u.forEach(o=>r.set(o)),r}static accessor(l){const r=(this[Xd]=this[Xd]={accessors:{}}).accessors,o=this.prototype;function d(m){const g=ri(m);r[g]||(jp(o,m),r[g]=!0)}return D.isArray(l)?l.forEach(d):d(l),this}};Et.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);D.reduceDescriptors(Et.prototype,({value:c},l)=>{let u=l[0].toUpperCase()+l.slice(1);return{get:()=>c,set(r){this[u]=r}}});D.freezeMethods(Et);function fr(c,l){const u=this||hi,r=l||u,o=Et.from(r.headers);let d=r.data;return D.forEach(c,function(g){d=g.call(u,d,o.normalize(),l?l.status:void 0)}),o.normalize(),d}function T2(c){return!!(c&&c.__CANCEL__)}function ul(c,l,u){be.call(this,c==null?"canceled":c,be.ERR_CANCELED,l,u),this.name="CanceledError"}D.inherits(ul,be,{__CANCEL__:!0});function w2(c,l,u){const r=u.config.validateStatus;!u.status||!r||r(u.status)?c(u):l(new be("Request failed with status code "+u.status,[be.ERR_BAD_REQUEST,be.ERR_BAD_RESPONSE][Math.floor(u.status/100)-4],u.config,u.request,u))}function _p(c){const l=/^([-+\w]{1,25})(:?\/\/|:)/.exec(c);return l&&l[1]||""}function Up(c,l){c=c||10;const u=new Array(c),r=new Array(c);let o=0,d=0,m;return l=l!==void 0?l:1e3,function(b){const p=Date.now(),S=r[d];m||(m=p),u[o]=b,r[o]=p;let R=d,H=0;for(;R!==o;)H+=u[R++],R=R%c;if(o=(o+1)%c,o===d&&(d=(d+1)%c),p-m<l)return;const Z=S&&p-S;return Z?Math.round(H*1e3/Z):void 0}}function Hp(c,l){let u=0,r=1e3/l,o,d;const m=(p,S=Date.now())=>{u=S,o=null,d&&(clearTimeout(d),d=null),c.apply(null,p)};return[(...p)=>{const S=Date.now(),R=S-u;R>=r?m(p,S):(o=p,d||(d=setTimeout(()=>{d=null,m(o)},r-R)))},()=>o&&m(o)]}const Ls=(c,l,u=3)=>{let r=0;const o=Up(50,250);return Hp(d=>{const m=d.loaded,g=d.lengthComputable?d.total:void 0,b=m-r,p=o(b),S=m<=g;r=m;const R={loaded:m,total:g,progress:g?m/g:void 0,bytes:b,rate:p||void 0,estimated:p&&g&&S?(g-m)/p:void 0,event:d,lengthComputable:g!=null,[l?"download":"upload"]:!0};c(R)},u)},$d=(c,l)=>{const u=c!=null;return[r=>l[0]({lengthComputable:u,total:c,loaded:r}),l[1]]},Kd=c=>(...l)=>D.asap(()=>c(...l)),kp=ht.hasStandardBrowserEnv?((c,l)=>u=>(u=new URL(u,ht.origin),c.protocol===u.protocol&&c.host===u.host&&(l||c.port===u.port)))(new URL(ht.origin),ht.navigator&&/(msie|trident)/i.test(ht.navigator.userAgent)):()=>!0,qp=ht.hasStandardBrowserEnv?{write(c,l,u,r,o,d){const m=[c+"="+encodeURIComponent(l)];D.isNumber(u)&&m.push("expires="+new Date(u).toGMTString()),D.isString(r)&&m.push("path="+r),D.isString(o)&&m.push("domain="+o),d===!0&&m.push("secure"),document.cookie=m.join("; ")},read(c){const l=document.cookie.match(new RegExp("(^|;\\s*)("+c+")=([^;]*)"));return l?decodeURIComponent(l[3]):null},remove(c){this.write(c,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Bp(c){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(c)}function Vp(c,l){return l?c.replace(/\/?\/$/,"")+"/"+l.replace(/^\/+/,""):c}function M2(c,l,u){let r=!Bp(l);return c&&(r||u==!1)?Vp(c,l):l}const Jd=c=>c instanceof Et?ne({},c):c;function vn(c,l){l=l||{};const u={};function r(p,S,R,H){return D.isPlainObject(p)&&D.isPlainObject(S)?D.merge.call({caseless:H},p,S):D.isPlainObject(S)?D.merge({},S):D.isArray(S)?S.slice():S}function o(p,S,R,H){if(D.isUndefined(S)){if(!D.isUndefined(p))return r(void 0,p,R,H)}else return r(p,S,R,H)}function d(p,S){if(!D.isUndefined(S))return r(void 0,S)}function m(p,S){if(D.isUndefined(S)){if(!D.isUndefined(p))return r(void 0,p)}else return r(void 0,S)}function g(p,S,R){if(R in l)return r(p,S);if(R in c)return r(void 0,p)}const b={url:d,method:d,data:d,baseURL:m,transformRequest:m,transformResponse:m,paramsSerializer:m,timeout:m,timeoutMessage:m,withCredentials:m,withXSRFToken:m,adapter:m,responseType:m,xsrfCookieName:m,xsrfHeaderName:m,onUploadProgress:m,onDownloadProgress:m,decompress:m,maxContentLength:m,maxBodyLength:m,beforeRedirect:m,transport:m,httpAgent:m,httpsAgent:m,cancelToken:m,socketPath:m,responseEncoding:m,validateStatus:g,headers:(p,S,R)=>o(Jd(p),Jd(S),R,!0)};return D.forEach(Object.keys(Object.assign({},c,l)),function(S){const R=b[S]||o,H=R(c[S],l[S],S);D.isUndefined(H)&&R!==g||(u[S]=H)}),u}const N2=c=>{const l=vn({},c);let{data:u,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:d,headers:m,auth:g}=l;l.headers=m=Et.from(m),l.url=S2(M2(l.baseURL,l.url,l.allowAbsoluteUrls),c.params,c.paramsSerializer),g&&m.set("Authorization","Basic "+btoa((g.username||"")+":"+(g.password?unescape(encodeURIComponent(g.password)):"")));let b;if(D.isFormData(u)){if(ht.hasStandardBrowserEnv||ht.hasStandardBrowserWebWorkerEnv)m.setContentType(void 0);else if((b=m.getContentType())!==!1){const[p,...S]=b?b.split(";").map(R=>R.trim()).filter(Boolean):[];m.setContentType([p||"multipart/form-data",...S].join("; "))}}if(ht.hasStandardBrowserEnv&&(r&&D.isFunction(r)&&(r=r(l)),r||r!==!1&&kp(l.url))){const p=o&&d&&qp.read(d);p&&m.set(o,p)}return l},Gp=typeof XMLHttpRequest!="undefined",Yp=Gp&&function(c){return new Promise(function(u,r){const o=N2(c);let d=o.data;const m=Et.from(o.headers).normalize();let{responseType:g,onUploadProgress:b,onDownloadProgress:p}=o,S,R,H,Z,j;function V(){Z&&Z(),j&&j(),o.cancelToken&&o.cancelToken.unsubscribe(S),o.signal&&o.signal.removeEventListener("abort",S)}let k=new XMLHttpRequest;k.open(o.method.toUpperCase(),o.url,!0),k.timeout=o.timeout;function ee(){if(!k)return;const P=Et.from("getAllResponseHeaders"in k&&k.getAllResponseHeaders()),w={data:!g||g==="text"||g==="json"?k.responseText:k.response,status:k.status,statusText:k.statusText,headers:P,config:c,request:k};w2(function(O){u(O),V()},function(O){r(O),V()},w),k=null}"onloadend"in k?k.onloadend=ee:k.onreadystatechange=function(){!k||k.readyState!==4||k.status===0&&!(k.responseURL&&k.responseURL.indexOf("file:")===0)||setTimeout(ee)},k.onabort=function(){k&&(r(new be("Request aborted",be.ECONNABORTED,c,k)),k=null)},k.onerror=function(){r(new be("Network Error",be.ERR_NETWORK,c,k)),k=null},k.ontimeout=function(){let Q=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const w=o.transitional||E2;o.timeoutErrorMessage&&(Q=o.timeoutErrorMessage),r(new be(Q,w.clarifyTimeoutError?be.ETIMEDOUT:be.ECONNABORTED,c,k)),k=null},d===void 0&&m.setContentType(null),"setRequestHeader"in k&&D.forEach(m.toJSON(),function(Q,w){k.setRequestHeader(w,Q)}),D.isUndefined(o.withCredentials)||(k.withCredentials=!!o.withCredentials),g&&g!=="json"&&(k.responseType=o.responseType),p&&([H,j]=Ls(p,!0),k.addEventListener("progress",H)),b&&k.upload&&([R,Z]=Ls(b),k.upload.addEventListener("progress",R),k.upload.addEventListener("loadend",Z)),(o.cancelToken||o.signal)&&(S=P=>{k&&(r(!P||P.type?new ul(null,c,k):P),k.abort(),k=null)},o.cancelToken&&o.cancelToken.subscribe(S),o.signal&&(o.signal.aborted?S():o.signal.addEventListener("abort",S)));const ue=_p(o.url);if(ue&&ht.protocols.indexOf(ue)===-1){r(new be("Unsupported protocol "+ue+":",be.ERR_BAD_REQUEST,c));return}k.send(d||null)})},Qp=(c,l)=>{const{length:u}=c=c?c.filter(Boolean):[];if(l||u){let r=new AbortController,o;const d=function(p){if(!o){o=!0,g();const S=p instanceof Error?p:this.reason;r.abort(S instanceof be?S:new ul(S instanceof Error?S.message:S))}};let m=l&&setTimeout(()=>{m=null,d(new be(`timeout ${l} of ms exceeded`,be.ETIMEDOUT))},l);const g=()=>{c&&(m&&clearTimeout(m),m=null,c.forEach(p=>{p.unsubscribe?p.unsubscribe(d):p.removeEventListener("abort",d)}),c=null)};c.forEach(p=>p.addEventListener("abort",d));const{signal:b}=r;return b.unsubscribe=()=>D.asap(g),b}},Zp=function*(c,l){let u=c.byteLength;if(u<l){yield c;return}let r=0,o;for(;r<u;)o=r+l,yield c.slice(r,o),r=o},Xp=function(c,l){return ar(this,null,function*(){try{for(var u=zd($p(c)),r,o,d;r=!(o=yield new ln(u.next())).done;r=!1){const m=o.value;yield*nr(Zp(m,l))}}catch(o){d=[o]}finally{try{r&&(o=u.return)&&(yield new ln(o.call(u)))}finally{if(d)throw d[0]}}})},$p=function(c){return ar(this,null,function*(){if(c[Symbol.asyncIterator]){yield*nr(c);return}const l=c.getReader();try{for(;;){const{done:u,value:r}=yield new ln(l.read());if(u)break;yield r}}finally{yield new ln(l.cancel())}})},Fd=(c,l,u,r)=>{const o=Xp(c,l);let d=0,m,g=p=>{m||(m=!0,r&&r(p))};return new ReadableStream({pull(p){return q(this,null,function*(){try{const{done:S,value:R}=yield o.next();if(S){g(),p.close();return}let H=R.byteLength;if(u){let Z=d+=H;u(Z)}p.enqueue(new Uint8Array(R))}catch(S){throw g(S),S}})},cancel(p){return g(p),o.return()}},{highWaterMark:2})},qs=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",R2=qs&&typeof ReadableStream=="function",Kp=qs&&(typeof TextEncoder=="function"?(c=>l=>c.encode(l))(new TextEncoder):c=>q(null,null,function*(){return new Uint8Array(yield new Response(c).arrayBuffer())})),x2=(c,...l)=>{try{return!!c(...l)}catch(u){return!1}},Jp=R2&&x2(()=>{let c=!1;const l=new Request(ht.origin,{body:new ReadableStream,method:"POST",get duplex(){return c=!0,"half"}}).headers.has("Content-Type");return c&&!l}),Wd=64*1024,vr=R2&&x2(()=>D.isReadableStream(new Response("").body)),Ds={stream:vr&&(c=>c.body)};qs&&(c=>{["text","arrayBuffer","blob","formData","stream"].forEach(l=>{!Ds[l]&&(Ds[l]=D.isFunction(c[l])?u=>u[l]():(u,r)=>{throw new be(`Response type '${l}' is not supported`,be.ERR_NOT_SUPPORT,r)})})})(new Response);const Fp=c=>q(null,null,function*(){if(c==null)return 0;if(D.isBlob(c))return c.size;if(D.isSpecCompliantForm(c))return(yield new Request(ht.origin,{method:"POST",body:c}).arrayBuffer()).byteLength;if(D.isArrayBufferView(c)||D.isArrayBuffer(c))return c.byteLength;if(D.isURLSearchParams(c)&&(c=c+""),D.isString(c))return(yield Kp(c)).byteLength}),Wp=(c,l)=>q(null,null,function*(){const u=D.toFiniteNumber(c.getContentLength());return u==null?Fp(l):u}),Pp=qs&&(c=>q(null,null,function*(){let{url:l,method:u,data:r,signal:o,cancelToken:d,timeout:m,onDownloadProgress:g,onUploadProgress:b,responseType:p,headers:S,withCredentials:R="same-origin",fetchOptions:H}=N2(c);p=p?(p+"").toLowerCase():"text";let Z=Qp([o,d&&d.toAbortSignal()],m),j;const V=Z&&Z.unsubscribe&&(()=>{Z.unsubscribe()});let k;try{if(b&&Jp&&u!=="get"&&u!=="head"&&(k=yield Wp(S,r))!==0){let w=new Request(l,{method:"POST",body:r,duplex:"half"}),G;if(D.isFormData(r)&&(G=w.headers.get("content-type"))&&S.setContentType(G),w.body){const[O,J]=$d(k,Ls(Kd(b)));r=Fd(w.body,Wd,O,J)}}D.isString(R)||(R=R?"include":"omit");const ee="credentials"in Request.prototype;j=new Request(l,ge(ne({},H),{signal:Z,method:u.toUpperCase(),headers:S.normalize().toJSON(),body:r,duplex:"half",credentials:ee?R:void 0}));let ue=yield fetch(j,H);const P=vr&&(p==="stream"||p==="response");if(vr&&(g||P&&V)){const w={};["status","statusText","headers"].forEach(de=>{w[de]=ue[de]});const G=D.toFiniteNumber(ue.headers.get("content-length")),[O,J]=g&&$d(G,Ls(Kd(g),!0))||[];ue=new Response(Fd(ue.body,Wd,O,()=>{J&&J(),V&&V()}),w)}p=p||"text";let Q=yield Ds[D.findKey(Ds,p)||"text"](ue,c);return!P&&V&&V(),yield new Promise((w,G)=>{w2(w,G,{data:Q,headers:Et.from(ue.headers),status:ue.status,statusText:ue.statusText,config:c,request:j})})}catch(ee){throw V&&V(),ee&&ee.name==="TypeError"&&/Load failed|fetch/i.test(ee.message)?Object.assign(new be("Network Error",be.ERR_NETWORK,c,j),{cause:ee.cause||ee}):be.from(ee,ee&&ee.code,c,j)}})),br={http:hp,xhr:Yp,fetch:Pp};D.forEach(br,(c,l)=>{if(c){try{Object.defineProperty(c,"name",{value:l})}catch(u){}Object.defineProperty(c,"adapterName",{value:l})}});const Pd=c=>`- ${c}`,Ip=c=>D.isFunction(c)||c===null||c===!1,O2={getAdapter:c=>{c=D.isArray(c)?c:[c];const{length:l}=c;let u,r;const o={};for(let d=0;d<l;d++){u=c[d];let m;if(r=u,!Ip(u)&&(r=br[(m=String(u)).toLowerCase()],r===void 0))throw new be(`Unknown adapter '${m}'`);if(r)break;o[m||"#"+d]=r}if(!r){const d=Object.entries(o).map(([g,b])=>`adapter ${g} `+(b===!1?"is not supported by the environment":"is not available in the build"));let m=l?d.length>1?`since :
`+d.map(Pd).join(`
`):" "+Pd(d[0]):"as no adapter specified";throw new be("There is no suitable adapter to dispatch the request "+m,"ERR_NOT_SUPPORT")}return r},adapters:br};function dr(c){if(c.cancelToken&&c.cancelToken.throwIfRequested(),c.signal&&c.signal.aborted)throw new ul(null,c)}function Id(c){return dr(c),c.headers=Et.from(c.headers),c.data=fr.call(c,c.transformRequest),["post","put","patch"].indexOf(c.method)!==-1&&c.headers.setContentType("application/x-www-form-urlencoded",!1),O2.getAdapter(c.adapter||hi.adapter)(c).then(function(r){return dr(c),r.data=fr.call(c,c.transformResponse,r),r.headers=Et.from(r.headers),r},function(r){return T2(r)||(dr(c),r&&r.response&&(r.response.data=fr.call(c,c.transformResponse,r.response),r.response.headers=Et.from(r.response.headers))),Promise.reject(r)})}const z2="1.10.0",Bs={};["object","boolean","number","function","string","symbol"].forEach((c,l)=>{Bs[c]=function(r){return typeof r===c||"a"+(l<1?"n ":" ")+c}});const e2={};Bs.transitional=function(l,u,r){function o(d,m){return"[Axios v"+z2+"] Transitional option '"+d+"'"+m+(r?". "+r:"")}return(d,m,g)=>{if(l===!1)throw new be(o(m," has been removed"+(u?" in "+u:"")),be.ERR_DEPRECATED);return u&&!e2[m]&&(e2[m]=!0,console.warn(o(m," has been deprecated since v"+u+" and will be removed in the near future"))),l?l(d,m,g):!0}};Bs.spelling=function(l){return(u,r)=>(console.warn(`${r} is likely a misspelling of ${l}`),!0)};function e4(c,l,u){if(typeof c!="object")throw new be("options must be an object",be.ERR_BAD_OPTION_VALUE);const r=Object.keys(c);let o=r.length;for(;o-- >0;){const d=r[o],m=l[d];if(m){const g=c[d],b=g===void 0||m(g,d,c);if(b!==!0)throw new be("option "+d+" must be "+b,be.ERR_BAD_OPTION_VALUE);continue}if(u!==!0)throw new be("Unknown option "+d,be.ERR_BAD_OPTION)}}const zs={assertOptions:e4,validators:Bs},Wt=zs.validators;let yn=class{constructor(l){this.defaults=l||{},this.interceptors={request:new Zd,response:new Zd}}request(l,u){return q(this,null,function*(){try{return yield this._request(l,u)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const d=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?d&&!String(r.stack).endsWith(d.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+d):r.stack=d}catch(m){}}throw r}})}_request(l,u){typeof l=="string"?(u=u||{},u.url=l):u=l||{},u=vn(this.defaults,u);const{transitional:r,paramsSerializer:o,headers:d}=u;r!==void 0&&zs.assertOptions(r,{silentJSONParsing:Wt.transitional(Wt.boolean),forcedJSONParsing:Wt.transitional(Wt.boolean),clarifyTimeoutError:Wt.transitional(Wt.boolean)},!1),o!=null&&(D.isFunction(o)?u.paramsSerializer={serialize:o}:zs.assertOptions(o,{encode:Wt.function,serialize:Wt.function},!0)),u.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?u.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:u.allowAbsoluteUrls=!0),zs.assertOptions(u,{baseUrl:Wt.spelling("baseURL"),withXsrfToken:Wt.spelling("withXSRFToken")},!0),u.method=(u.method||this.defaults.method||"get").toLowerCase();let m=d&&D.merge(d.common,d[u.method]);d&&D.forEach(["delete","get","head","post","put","patch","common"],j=>{delete d[j]}),u.headers=Et.concat(m,d);const g=[];let b=!0;this.interceptors.request.forEach(function(V){typeof V.runWhen=="function"&&V.runWhen(u)===!1||(b=b&&V.synchronous,g.unshift(V.fulfilled,V.rejected))});const p=[];this.interceptors.response.forEach(function(V){p.push(V.fulfilled,V.rejected)});let S,R=0,H;if(!b){const j=[Id.bind(this),void 0];for(j.unshift.apply(j,g),j.push.apply(j,p),H=j.length,S=Promise.resolve(u);R<H;)S=S.then(j[R++],j[R++]);return S}H=g.length;let Z=u;for(R=0;R<H;){const j=g[R++],V=g[R++];try{Z=j(Z)}catch(k){V.call(this,k);break}}try{S=Id.call(this,Z)}catch(j){return Promise.reject(j)}for(R=0,H=p.length;R<H;)S=S.then(p[R++],p[R++]);return S}getUri(l){l=vn(this.defaults,l);const u=M2(l.baseURL,l.url,l.allowAbsoluteUrls);return S2(u,l.params,l.paramsSerializer)}};D.forEach(["delete","get","head","options"],function(l){yn.prototype[l]=function(u,r){return this.request(vn(r||{},{method:l,url:u,data:(r||{}).data}))}});D.forEach(["post","put","patch"],function(l){function u(r){return function(d,m,g){return this.request(vn(g||{},{method:l,headers:r?{"Content-Type":"multipart/form-data"}:{},url:d,data:m}))}}yn.prototype[l]=u(),yn.prototype[l+"Form"]=u(!0)});let t4=class L2{constructor(l){if(typeof l!="function")throw new TypeError("executor must be a function.");let u;this.promise=new Promise(function(d){u=d});const r=this;this.promise.then(o=>{if(!r._listeners)return;let d=r._listeners.length;for(;d-- >0;)r._listeners[d](o);r._listeners=null}),this.promise.then=o=>{let d;const m=new Promise(g=>{r.subscribe(g),d=g}).then(o);return m.cancel=function(){r.unsubscribe(d)},m},l(function(d,m,g){r.reason||(r.reason=new ul(d,m,g),u(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(l){if(this.reason){l(this.reason);return}this._listeners?this._listeners.push(l):this._listeners=[l]}unsubscribe(l){if(!this._listeners)return;const u=this._listeners.indexOf(l);u!==-1&&this._listeners.splice(u,1)}toAbortSignal(){const l=new AbortController,u=r=>{l.abort(r)};return this.subscribe(u),l.signal.unsubscribe=()=>this.unsubscribe(u),l.signal}static source(){let l;return{token:new L2(function(o){l=o}),cancel:l}}};function a4(c){return function(u){return c.apply(null,u)}}function n4(c){return D.isObject(c)&&c.isAxiosError===!0}const Cr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Cr).forEach(([c,l])=>{Cr[l]=c});function D2(c){const l=new yn(c),u=r2(yn.prototype.request,l);return D.extend(u,yn.prototype,l,{allOwnKeys:!0}),D.extend(u,l,null,{allOwnKeys:!0}),u.create=function(o){return D2(vn(c,o))},u}const Ve=D2(hi);Ve.Axios=yn;Ve.CanceledError=ul;Ve.CancelToken=t4;Ve.isCancel=T2;Ve.VERSION=z2;Ve.toFormData=ks;Ve.AxiosError=be;Ve.Cancel=Ve.CanceledError;Ve.all=function(l){return Promise.all(l)};Ve.spread=a4;Ve.isAxiosError=n4;Ve.mergeConfig=vn;Ve.AxiosHeaders=Et;Ve.formToJSON=c=>A2(D.isHTMLForm(c)?new FormData(c):c);Ve.getAdapter=O2.getAdapter;Ve.HttpStatusCode=Cr;Ve.default=Ve;const{Axios:M3,AxiosError:N3,CanceledError:R3,isCancel:x3,CancelToken:O3,VERSION:z3,all:L3,Cancel:D3,isAxiosError:j3,spread:_3,toFormData:U3,AxiosHeaders:H3,HttpStatusCode:k3,formToJSON:q3,getAdapter:B3,mergeConfig:V3}=Ve,cn=class cn{constructor(){me(this,"accentMap",new Map([["á","a"],["à","a"],["ä","a"],["â","a"],["ā","a"],["ã","a"],["é","e"],["è","e"],["ë","e"],["ê","e"],["ē","e"],["í","i"],["ì","i"],["ï","i"],["î","i"],["ī","i"],["ó","o"],["ò","o"],["ö","o"],["ô","o"],["ō","o"],["õ","o"],["ú","u"],["ù","u"],["ü","u"],["û","u"],["ū","u"],["ñ","n"],["ç","c"],["Á","A"],["À","A"],["Ä","A"],["Â","A"],["Ā","A"],["Ã","A"],["É","E"],["È","E"],["Ë","E"],["Ê","E"],["Ē","E"],["Í","I"],["Ì","I"],["Ï","I"],["Î","I"],["Ī","I"],["Ó","O"],["Ò","O"],["Ö","O"],["Ô","O"],["Ō","O"],["Õ","O"],["Ú","U"],["Ù","U"],["Ü","U"],["Û","U"],["Ū","U"],["Ñ","N"],["Ç","C"]]));me(this,"htmlChars",/[<>\"'&]/g);me(this,"scriptChars",/[<>\"'&{}()[\]]/g)}static getInstance(){return cn.instance||(cn.instance=new cn),cn.instance}normalize(l,u={}){if(!l)return"";const{removeAccents:r=!0,toLowerCase:o=!0,trimWhitespace:d=!0,removeSpecialChars:m=!1,maxLength:g,preserveSpaces:b=!0}=u;let p=l;return o&&(p=p.toLowerCase()),r&&(p=this.removeAccents(p)),d&&(b?p=p.replace(/\s+/g," ").trim():p=p.replace(/\s/g,"")),m&&(p=p.replace(/[^\w\s]/g,"")),g&&p.length>g&&(p=p.substring(0,g)),p}normalizeAsync(r){return q(this,arguments,function*(l,u={}){return Promise.resolve(this.normalize(l,u))})}sanitize(l,u={}){if(!l)return"";const{maxLength:r=1e3,removeHtmlChars:o=!0,removeScriptChars:d=!1,preserveBasicPunctuation:m=!0}=u;let g=l;return o&&(g=g.replace(this.htmlChars,"")),d&&(g=g.replace(this.scriptChars,"")),m||(g=g.replace(/[^\w\s]/g,"")),g=g.substring(0,r).trim(),g}removeAccents(l){let u=l;for(const[r,o]of this.accentMap.entries()){const d=new RegExp(r,"g");u=u.replace(d,o)}return u}normalizeForSpeech(l){return this.normalize(l,{removeAccents:!0,toLowerCase:!0,trimWhitespace:!0,removeSpecialChars:!1,preserveSpaces:!0})}normalizeForGameResponse(l){return this.normalize(l,{removeAccents:!0,toLowerCase:!0,trimWhitespace:!0,removeSpecialChars:!1,preserveSpaces:!0,maxLength:200})}sanitizeForAPI(l){return this.sanitize(l,{maxLength:1e3,removeHtmlChars:!0,removeScriptChars:!0,preserveBasicPunctuation:!0})}isEmpty(l){return this.normalize(l).length===0}isValid(l,u=1,r=1e3){if(!l)return!1;const o=this.normalize(l);return o.length>=u&&o.length<=r}areEqual(l,u,r={}){const o=this.normalize(l,r),d=this.normalize(u,r);return o===d}contains(l,u,r={}){const o=this.normalize(l,r),d=this.normalize(u,r);return o.includes(d)}};me(cn,"instance");let Sr=cn;const l4=Sr.getInstance();function t2(c){return q(this,null,function*(){try{return(yield c).data}catch(l){throw l}})}function i4(c="req"){const l=Date.now(),u=Math.random().toString(36).substring(2,8);return`${c}-${l}-${u}`}const j2=C.createContext(void 0),s4=()=>{const c=C.useContext(j2);if(!c)throw new Error("useAppContext must be used within an AppProvider");return c},c4=({children:c})=>{const[l,u]=C.useState("initializing"),[r,o]=C.useState(!1),[d,m]=C.useState([]),[g,b]=C.useState("main"),[p,S]=C.useState(()=>({iaApiUrl:"https://dev.dl2discovery.org/llm-api/v1/",iaApiKey:"9dcd0147-11e2-4e9e-aaf3-05e1498ce828",speechApiUrl:"https://dev.dl2discovery.org/sts/api/v1/",speechApiKey:"************************************"}));C.useEffect(()=>{P()},[]),C.useEffect(()=>{const G=J=>{H(`Error global: ${J.message}`,`${J.filename}:${J.lineno}:${J.colno}`,J.error)},O=J=>{H(`Promise rechazada: ${J.reason}`,"Unhandled Promise Rejection",J.reason instanceof Error?J.reason:void 0)};return window.addEventListener("error",G),window.addEventListener("unhandledrejection",O),()=>{window.removeEventListener("error",G),window.removeEventListener("unhandledrejection",O)}},[]);const R=C.useCallback(G=>{m(O=>[...O,G])},[]),H=C.useCallback((G,O,J)=>{const de={id:i4("error"),message:G,timestamp:new Date,context:O,error:J};R(de)},[R]),Z=C.useCallback(G=>{S(O=>ne(ne({},O),G))},[]),j=C.useCallback(G=>{b(G)},[]),V=C.useCallback(()=>{m([])},[]),k=C.useCallback(G=>{m(O=>O.filter(J=>J.id!==G))},[]),ee=C.useCallback(()=>!!(p.iaApiUrl&&p.iaApiKey&&p.speechApiUrl&&p.speechApiKey),[p]),ue=C.useCallback(()=>({iaApiUrl:!!p.iaApiUrl,iaApiKey:!!p.iaApiKey,speechApiUrl:!!p.speechApiUrl,speechApiKey:!!p.speechApiKey}),[p]),P=C.useCallback(()=>q(null,null,function*(){try{if(u("initializing"),!ee())throw new Error("Configuración incompleta: faltan variables de entorno críticas");u("ready"),o(!0),console.log("✅ [AppContext] Aplicación inicializada correctamente",{config:ue(),state:"ready",initialized:!0})}catch(G){const O=G instanceof Error?G.message:"Error desconocido";u("error"),H(`Error en inicialización: ${O}`,"initialize",G instanceof Error?G:void 0)}}),[ee,R]),Q=C.useCallback(()=>{u("initializing"),o(!1),V(),P()},[P,V]),w=C.useMemo(()=>({appState:l,isInitialized:r,config:p,updateConfig:Z,errors:d,addError:R,clearErrors:V,clearError:k,currentView:g,navigate:j,initialize:P,reset:Q}),[l,r,p,Z,d,R,V,k,g,j,P,Q]);return A.jsx(j2.Provider,{value:w,children:c})},u4=C.createContext(void 0),r4=({children:c})=>{const[l,u]=C.useState(new Map),[r,o]=C.useState([]),d=C.useCallback((b,p)=>{const S={type:b,payload:p,timestamp:new Date};o(H=>[S,...H.slice(0,99)]);const R=l.get(b);R&&R.forEach(H=>{try{H(S)}catch(Z){console.error(`Error in event listener for ${b}:`,Z)}})},[l]),m=C.useCallback((b,p)=>(u(S=>{const R=new Map(S);return R.has(b)||R.set(b,new Set),R.get(b).add(p),R}),()=>{u(S=>{const R=new Map(S),H=R.get(b);return H&&(H.delete(p),H.size===0&&R.delete(b)),R})}),[]),g=C.useCallback(b=>r.find(p=>p.type===b)||null,[r]);return A.jsx(u4.Provider,{value:{emit:d,subscribe:m,getLastEvent:g},children:c})},ct=[];for(let c=0;c<256;++c)ct.push((c+256).toString(16).slice(1));function o4(c,l=0){return(ct[c[l+0]]+ct[c[l+1]]+ct[c[l+2]]+ct[c[l+3]]+"-"+ct[c[l+4]]+ct[c[l+5]]+"-"+ct[c[l+6]]+ct[c[l+7]]+"-"+ct[c[l+8]]+ct[c[l+9]]+"-"+ct[c[l+10]]+ct[c[l+11]]+ct[c[l+12]]+ct[c[l+13]]+ct[c[l+14]]+ct[c[l+15]]).toLowerCase()}let hr;const f4=new Uint8Array(16);function d4(){if(!hr){if(typeof crypto=="undefined"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");hr=crypto.getRandomValues.bind(crypto)}return hr(f4)}const h4=typeof crypto!="undefined"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),a2={randomUUID:h4};function n2(c,l,u){var o,d,m;if(a2.randomUUID&&!c)return a2.randomUUID();c=c||{};const r=(m=(d=c.random)!=null?d:(o=c.rng)==null?void 0:o.call(c))!=null?m:d4();if(r.length<16)throw new Error("Random bytes length must be >= 16");return r[6]=r[6]&15|64,r[8]=r[8]&63|128,o4(r)}const Er=c=>l4.normalizeAsync(c,{removeAccents:!0,toLowerCase:!0,trimWhitespace:!0,preserveSpaces:!0}),un=class un{constructor(){me(this,"mhcWarningShown",!1)}static getInstance(){return un.instance||(un.instance=new un),un.instance}isMHAvailable(){return typeof MH!="undefined"}setSucwTimeout(l){if(this.isMHAvailable())try{MH.sucwTimeout(l)}catch(u){}}closeWebView(){if(this.isMHAvailable())try{MH.closeWebview()}catch(l){}}hideAura(){if(this.isMHAvailable())try{MH.hideAura()}catch(l){}}speakAura(l){if(this.isMHAvailable())try{const u=JSON.stringify({text:l});MH.speak(u)}catch(u){}}onPageLoaded(){if(this.isMHAvailable())try{MH.onPageLoaded()}catch(l){}else this.mhcWarningShown}sendAura(l){if(this.isMHAvailable())try{const u=JSON.stringify({text:l});MH.sendAura(u)}catch(u){}}getId(){if(this.isMHAvailable())try{return MH.getId()}catch(l){return n2()}else return this.mhcWarningShown||(this.mhcWarningShown=!0),n2()}isAvailable(){return this.isMHAvailable()}getStatus(){return{available:this.isMHAvailable(),id:this.getId(),timestamp:new Date().toISOString()}}};me(un,"instance");let oi=un;const rn=class rn{constructor(){me(this,"mhc");me(this,"currentTranscription",null);me(this,"eventListeners",new Set);me(this,"isListening",!1);me(this,"CLOSE_COMMANDS",["salir","cerrar","exit","close"]);me(this,"handleTranscriptionEvent",l=>{const r=l.detail;this.processTranscription(r)});this.mhc=oi.getInstance(),this.setupGlobalTesting()}static getInstance(){return rn.instance||(rn.instance=new rn),rn.instance}getCurrentTranscription(){return this.currentTranscription}isCurrentlyListening(){return this.isListening}getMHC(){return this.mhc}setupGlobalTesting(){try{window.testTranscription=l=>{this.simulateTranscription(l)},window.transcriptionService=this}catch(l){}}processTranscription(l){return q(this,null,function*(){try{if(!l||l.trim()==="")return{success:!1,error:"Transcripción vacía"};const u=yield Er(l);if(this.CLOSE_COMMANDS.includes(u))return this.handleCloseCommand(u),{success:!0,transcription:l,normalized:u};this.currentTranscription=u;const r={type:"transcription",data:l,normalized:u,timestamp:new Date};return this.notifyListeners(r),setTimeout(()=>{this.mhc.hideAura()},1e3),{success:!0,transcription:l,normalized:u}}catch(u){const r={type:"error",data:u instanceof Error?u.message:"Error desconocido",timestamp:new Date};return this.notifyListeners(r),{success:!1,error:u instanceof Error?u.message:"Error desconocido"}}})}handleCloseCommand(l){const u={type:"command",data:l,timestamp:new Date};this.notifyListeners(u),this.mhc.closeWebView()}notifyListeners(l){this.eventListeners.forEach(u=>{try{u(l)}catch(r){}})}startListening(){this.isListening||(window.addEventListener("transcriptionEvent",this.handleTranscriptionEvent),this.isListening=!0,this.mhc.onPageLoaded())}stopListening(){this.isListening&&(window.removeEventListener("transcriptionEvent",this.handleTranscriptionEvent),this.isListening=!1)}addEventListener(l){return this.eventListeners.add(l),()=>{this.eventListeners.delete(l)}}simulateTranscription(l){const u=new CustomEvent("transcriptionEvent",{detail:l});window.dispatchEvent(u)}clearTranscription(){this.currentTranscription=null}reset(){this.stopListening(),this.clearTranscription(),this.eventListeners.clear()}setSucwTimeout(l){this.mhc.setSucwTimeout(l)}closeWebView(){this.mhc.closeWebView()}hideAura(){this.mhc.hideAura()}speakAura(l){this.mhc.speakAura(l)}sendAura(l){this.mhc.sendAura(l)}getId(){return this.mhc.getId()}isMHCAvailable(){return this.mhc instanceof oi&&this.mhc.isAvailable()}getStatus(){return{listening:this.isListening,currentTranscription:this.currentTranscription,listenersCount:this.eventListeners.size,mhcAvailable:this.isMHCAvailable(),mhcStatus:this.mhc instanceof oi?this.mhc.getStatus():null}}getCloseCommands(){return[...this.CLOSE_COMMANDS]}addCloseCommand(l){const u=l.toLowerCase().trim();this.CLOSE_COMMANDS.includes(u)||this.CLOSE_COMMANDS.push(u)}testNormalization(l){return q(this,null,function*(){return yield Er(l)})}destroy(){this.reset();try{delete window.testTranscription,delete window.transcriptionService}catch(l){}}};me(rn,"instance");let Ar=rn;const ut=Ar.getInstance(),m4=C.createContext(void 0),p4=({children:c})=>{const[l,u]=C.useState({isAvailable:!1,deviceId:"",connectionStatus:"disconnected",capabilities:{canSpeak:!1,canCloseWebView:!1,canSetTimeout:!1,canHideAura:!1,canSendMessages:!1},errorMessage:null}),[r,o]=C.useState(null),d=C.useCallback(()=>q(null,null,function*(){if(!r)return{canSpeak:!1,canCloseWebView:!1,canSetTimeout:!1,canHideAura:!1,canSendMessages:!1};const w={canSpeak:!1,canCloseWebView:!1,canSetTimeout:!1,canHideAura:!1,canSendMessages:!1};try{r.getId(),w.canSpeak=!0,w.canCloseWebView=!0,w.canSetTimeout=!0,w.canHideAura=!0,w.canSendMessages=!0}catch(G){console.warn("⚠️ Algunas funcionalidades MHC no están disponibles:",G)}return w}),[r]),m=C.useCallback(()=>q(null,null,function*(){u(w=>ge(ne({},w),{connectionStatus:"connecting"}));try{const w=ut.getMHC();if(o(w),ut.isMHCAvailable()){const O=w.getId();let J={canSpeak:!1,canCloseWebView:!1,canSetTimeout:!1,canHideAura:!1,canSendMessages:!1};try{w.getId(),J={canSpeak:!0,canCloseWebView:!0,canSetTimeout:!0,canHideAura:!0,canSendMessages:!0}}catch(de){console.warn("⚠️ Algunas funcionalidades MHC no están disponibles:",de)}u(de=>ge(ne({},de),{isAvailable:!0,deviceId:O,connectionStatus:"connected",capabilities:J,errorMessage:null})),console.log("✅ MHC inicializado correctamente",{deviceId:O,capabilities:J})}else u(O=>ge(ne({},O),{isAvailable:!1,connectionStatus:"disconnected",errorMessage:"MHC no disponible en este dispositivo"})),console.warn("⚠️ MHC no disponible")}catch(w){const G=w instanceof Error?w.message:"Error inicializando MHC";u(O=>ge(ne({},O),{connectionStatus:"error",errorMessage:G})),console.error("❌ Error inicializando MHC:",w)}}),[]);C.useEffect(()=>(console.log("✅ [MHCProvider] Inicializando MHC"),m(),()=>{console.log("ℹ️ [MHCProvider] Limpiando MHCProvider")}),[m]);const g=C.useCallback(w=>{if(!r||!l.isAvailable){console.warn("⚠️ [MHCProvider] MHC no disponible para setSucwTimeout");return}try{r.setSucwTimeout(w),console.log(`✅ [MHCProvider] Timeout establecido: ${w}ms`)}catch(G){console.error("❌ [MHCProvider] Error estableciendo timeout:",G)}},[r,l.isAvailable]),b=C.useCallback(()=>{if(!r||!l.isAvailable){console.warn("⚠️ [MHCProvider] MHC no disponible para closeWebView");return}try{r.closeWebView(),console.log("✅ [MHCProvider] WebView cerrado")}catch(w){console.error("❌ [MHCProvider] Error cerrando WebView:",w)}},[r,l.isAvailable]),p=C.useCallback(()=>{if(!r||!l.isAvailable){console.warn("⚠️ [MHCProvider] MHC no disponible para hideAura");return}try{r.hideAura(),console.log("✅ [MHCProvider] Aura ocultado")}catch(w){console.error("❌ [MHCProvider] Error ocultando Aura:",w)}},[r,l.isAvailable]),S=C.useCallback(w=>{if(!r||!l.isAvailable){console.warn("⚠️ [MHCProvider] MHC no disponible para speakAura");return}try{r.speakAura(w),console.log(`✅ [MHCProvider] Aura hablando: "${w.substring(0,50)}..."`)}catch(G){console.error("❌ [MHCProvider] Error haciendo hablar a Aura:",G)}},[r,l.isAvailable]),R=C.useCallback(w=>{if(!r||!l.isAvailable){console.warn("⚠️ [MHCProvider] MHC no disponible para sendAura");return}try{r.sendAura(w),console.log(`✅ [MHCProvider] Mensaje enviado a Aura: "${w.substring(0,50)}..."`)}catch(G){console.error("❌ [MHCProvider] Error enviando mensaje a Aura:",G)}},[r,l.isAvailable]),H=C.useCallback(()=>{if(!r)return console.warn("⚠️ [MHCProvider] MHC no disponible para getId, usando fallback"),`fallback-${Date.now()}`;try{const w=r.getId();return console.log(`🔍 [MHCProvider] 🆔 Device ID: ${w}`),w}catch(w){return console.error("❌ [MHCProvider] ❌ Error obteniendo ID:",w),`error-${Date.now()}`}},[r]),Z=C.useCallback((w,G=2e3)=>q(null,null,function*(){if(!l.capabilities.canSpeak||!l.capabilities.canHideAura){console.warn("⚠️ [MHCProvider] Funcionalidades requeridas no disponibles para speakAndHide");return}try{console.log(`ℹ️ [MHCProvider] Hablar y ocultar: "${w.substring(0,50)}..."`),r&&(r.speakAura(w),setTimeout(()=>{r&&r.hideAura()},G))}catch(O){console.error("❌ [MHCProvider] Error en speakAndHide:",O)}}),[l.capabilities.canSpeak,l.capabilities.canHideAura,r]),j=C.useCallback(w=>{const G=w*1e3;if(r&&l.isAvailable)try{r.setSucwTimeout(G),console.log(`✅ [MHCProvider] Timeout de aplicación establecido: ${w}s`)}catch(O){console.error("❌ [MHCProvider] Error estableciendo timeout de app:",O)}},[r,l.isAvailable]),V=C.useCallback(()=>q(null,null,function*(){try{if(!r)return!1;const G=!!r.getId();return u(O=>ge(ne({},O),{connectionStatus:G?"connected":"disconnected"})),G}catch(w){return u(G=>ge(ne({},G),{connectionStatus:"error",errorMessage:"Error verificando conexión MHC"})),!1}}),[r]),k=C.useCallback(()=>q(null,null,function*(){console.log("ℹ️ [MHCProvider] 🔄 Intentando reconexión MHC"),u(w=>ge(ne({},w),{connectionStatus:"connecting"}));try{return yield m(),ut.isMHCAvailable()}catch(w){return console.error("❌ [MHCProvider] ❌ Error en reconexión:",w),!1}}),[m]),ee=C.useCallback(()=>({isAvailable:l.isAvailable,deviceId:l.deviceId,connectionStatus:l.connectionStatus,capabilities:l.capabilities,mhcVersion:"unknown",timestamp:new Date().toISOString()}),[l]),ue=C.useCallback(w=>l.capabilities[w],[l.capabilities]),P=C.useCallback(()=>{console.log("ℹ️ [MHCProvider] 🔄 Reseteando MHC"),u({isAvailable:!1,deviceId:"",connectionStatus:"disconnected",capabilities:{canSpeak:!1,canCloseWebView:!1,canSetTimeout:!1,canHideAura:!1,canSendMessages:!1},errorMessage:null}),o(null)},[]),Q=C.useMemo(()=>({state:l,setSucwTimeout:g,closeWebView:b,hideAura:p,speakAura:S,sendAura:R,getId:H,speakAndHide:Z,setAppTimeout:j,checkConnection:V,reconnect:k,getDeviceInfo:ee,isFeatureAvailable:ue,testAllFeatures:d,reset:P}),[l,g,b,p,S,R,H,Z,j,V,k,ee,ue,d,P]);return A.jsx(m4.Provider,{value:Q,children:c})},on=class on{constructor(){me(this,"exactPatterns",new Map);me(this,"fuzzyPatterns",[]);me(this,"contextualPatterns",new Map);this.initializePatterns()}static getInstance(){return on.instance||(on.instance=new on),on.instance}initializePatterns(){const l={sí:"yes",si:"yes",yes:"yes",claro:"yes",obvio:"yes",vale:"yes",okay:"yes",ok:"yes",correcto:"yes",exacto:"yes",cierto:"yes",verdad:"yes","por supuesto":"yes","desde luego":"yes","sin duda":"yes",efectivamente:"yes","así es":"yes",no:"no",nope:"no","para nada":"no","de ninguna manera":"no","en absoluto":"no",jamás:"no",nunca:"no",imposible:"no","ni hablar":"no","ni de coña":"no","qué va":"no","que va":"no","tal vez":"maybe",quizás:"maybe",quizas:"maybe","puede ser":"maybe","es posible":"maybe",posiblemente:"maybe","a lo mejor":"maybe","no estoy seguro":"maybe","no estoy segura":"maybe",depende:"maybe","no lo sé":"unknown","no sé":"unknown","no se":"unknown","no tengo ni idea":"unknown","ni idea":"unknown","no idea":"unknown","no sabría decir":"unknown","no sabria decir":"unknown"};for(const[u,r]of Object.entries(l))this.exactPatterns.set(u,r);this.contextualPatterns.set("creo que sí","maybe"),this.contextualPatterns.set("creo que no","maybe"),this.contextualPatterns.set("me parece que","maybe"),this.contextualPatterns.set("diría que","maybe"),this.fuzzyPatterns=[{pattern:/^(sí|si|yes|claro|obvio)[\s.,!]*$/i,type:"yes",confidence:.95},{pattern:/^(vale|okay|ok)[\s.,!]*$/i,type:"yes",confidence:.85},{pattern:/(correcto|exacto|cierto|verdad)/i,type:"yes",confidence:.9},{pattern:/(por\s*supuesto|desde\s*luego|sin\s*duda)/i,type:"yes",confidence:.9},{pattern:/^no[\s.,!]*$/i,type:"no",confidence:.95},{pattern:/(para\s*nada|de\s*ninguna\s*manera|en\s*absoluto)/i,type:"no",confidence:.9},{pattern:/(jamás|nunca|imposible)/i,type:"no",confidence:.85},{pattern:/(ni\s*hablar|ni\s*de\s*coña|qué\s*va)/i,type:"no",confidence:.8},{pattern:/(tal\s*vez|quizás?|puede\s*ser)/i,type:"maybe",confidence:.9},{pattern:/(es\s*posible|posiblemente|a\s*lo\s*mejor)/i,type:"maybe",confidence:.85},{pattern:/(no\s*estoy\s*segur[oa]|depende)/i,type:"maybe",confidence:.8},{pattern:/(no\s*lo?\s*sé|no\s*se)/i,type:"unknown",confidence:.95},{pattern:/(no\s*tengo\s*ni\s*idea|ni\s*idea)/i,type:"unknown",confidence:.9},{pattern:/(no\s*sabría?\s*decir)/i,type:"unknown",confidence:.85}]}validate(l){if(!(l!=null&&l.trim()))return{type:"invalid",confidence:0,alternatives:[],suggestions:["Intenta decir: Sí, No, Tal vez, o No lo sé"],isAmbiguous:!1};const u=l.toLowerCase().trim(),r=[],o=this.exactPatterns.get(u);if(o)return{type:o,confidence:1,alternatives:[],isAmbiguous:!1};for(const{pattern:b,type:p,confidence:S}of this.fuzzyPatterns)b.test(u)&&r.push({type:p,confidence:S,source:"fuzzy"});for(const[b,p]of this.contextualPatterns.entries())u.includes(b)&&r.push({type:p,confidence:.5,source:"contextual"});for(const[b,p]of this.exactPatterns.entries())u.includes(b)&&b.length>2&&r.push({type:p,confidence:.4,source:"inclusion"});if(r.length===0)return{type:"invalid",confidence:0,alternatives:[],suggestions:this.generateSuggestions(u),isAmbiguous:!1};const d=this.groupResultsByType(r),m=this.selectBestResult(d),g=this.getAlternatives(d,m.type);return{type:m.type,confidence:m.confidence,alternatives:g,isAmbiguous:g.length>0&&m.confidence<.8,suggestions:m.confidence<.6?this.generateSuggestions(u):void 0}}groupResultsByType(l){const u=new Map;for(const r of l){const o=u.get(r.type);o?u.set(r.type,{confidence:Math.max(o.confidence,r.confidence),count:o.count+1}):u.set(r.type,{confidence:r.confidence,count:1})}return u}selectBestResult(l){var o;let u="invalid",r=0;for(const[d,m]of l.entries()){const g=m.confidence*(1+m.count*.1);g>r&&(r=g,u=d)}return{type:u,confidence:((o=l.get(u))==null?void 0:o.confidence)||0}}getAlternatives(l,u){return Array.from(l.entries()).filter(([r,o])=>r!==u&&o.confidence>.3).sort(([,r],[,o])=>o.confidence-r.confidence).map(([r])=>r).slice(0,2)}generateSuggestions(l){const u=["Intenta decir: Sí, No, Tal vez, o No lo sé"];return l.includes("si")||l.includes("yes")?u.unshift("¿Quisiste decir 'Sí'?"):l.includes("no")?u.unshift("¿Quisiste decir 'No'?"):(l.includes("vez")||l.includes("quiza"))&&u.unshift("¿Quisiste decir 'Tal vez'?"),u}isValidResponse(l,u=.6){const r=this.validate(l);return r.type!=="invalid"&&r.confidence>=u}getResponseType(l,u=.6){const r=this.validate(l);return r.confidence>=u?r.type:"invalid"}getConfidenceScore(l){return this.validate(l).confidence}getAlternativeResponses(l){return this.validate(l).alternatives}isAmbiguous(l){return this.validate(l).isAmbiguous}getSuggestions(l){return this.validate(l).suggestions||[]}getSupportedResponses(){return["Sí / No","Tal vez / Quizás","No lo sé / No sé","Es posible / Puede ser"]}getResponseHelp(){return"Responde con: Sí, No, Tal vez, o No lo sé. También puedes usar variaciones como 'Claro', 'Para nada', 'Quizás', etc."}};me(on,"instance");let Tr=on;const ma=Tr.getInstance(),_2=C.createContext(void 0),g4=()=>{const c=C.useContext(_2);if(!c)throw new Error("useSpeechInput must be used within SpeechInputProvider");return c},y4=({children:c})=>{const[l,u]=C.useState({transcription:null,isListening:!1,isProcessing:!1,confidence:0,lastValidatedResponse:null,errorMessage:null}),r=C.useRef(null),o=C.useMemo(()=>ma.getSupportedResponses(),[]),d=C.useMemo(()=>ma.getResponseHelp(),[]),m=C.useCallback(O=>{switch(O.type){case"transcription":{const J=O.normalized||O.data,de=ma.validate(J);u(Le=>ge(ne({},Le),{transcription:J,isProcessing:!1,errorMessage:null,lastValidatedResponse:de.type,confidence:de.confidence}));break}case"error":u(J=>ge(ne({},J),{isProcessing:!1,errorMessage:O.data}));break}},[]);C.useEffect(()=>{const O=ut.addEventListener(m),J=ut.isCurrentlyListening();return u(de=>ge(ne({},de),{isListening:J})),()=>{O(),r.current&&r.current.abort()}},[m]);const g=C.useCallback(()=>{u(O=>ge(ne({},O),{isProcessing:!0,errorMessage:null}));try{ut.startListening(),u(O=>ge(ne({},O),{isListening:!0,isProcessing:!1}))}catch(O){u(J=>ge(ne({},J),{isProcessing:!1,errorMessage:"Error al iniciar el micrófono"}))}},[]),b=C.useCallback(()=>{try{ut.stopListening(),u(O=>ge(ne({},O),{isListening:!1}))}catch(O){}},[]),p=C.useCallback(()=>{ut.clearTranscription(),u(O=>ge(ne({},O),{transcription:null,lastValidatedResponse:null,errorMessage:null,confidence:0}))},[]),S=C.useCallback(()=>{r.current&&r.current.abort(),ut.reset(),u({transcription:null,isListening:!1,isProcessing:!1,confidence:0,lastValidatedResponse:null,errorMessage:null})},[]),R=C.useCallback(O=>{ut.simulateTranscription(O)},[]),H=C.useCallback(O=>ma.getResponseType(O),[]),Z=C.useCallback(O=>ma.getConfidenceScore(O),[]),j=C.useCallback(O=>ma.getAlternativeResponses(O),[]),V=C.useCallback(O=>ma.isAmbiguous(O),[]),k=C.useCallback((O=3e4)=>new Promise((J,de)=>{const Le=new AbortController;r.current=Le;let xe=!1;const Se=oe=>{if(xe||Le.signal.aborted||oe.type!=="transcription")return;const L=ma.validate(oe.normalized||oe.data);L.type!=="invalid"&&L.confidence>.6&&(xe=!0,clearTimeout(re),te(),J(L.type))},te=ut.addEventListener(Se),re=setTimeout(()=>{xe||(xe=!0,Le.abort(),te(),de(new Error("Timeout esperando respuesta válida")))},O);Le.signal.addEventListener("abort",()=>{xe||(xe=!0,clearTimeout(re),te(),de(new Error("Operation aborted")))})}),[]),ee=C.useCallback((O,J=3e4)=>new Promise((de,Le)=>{const xe=new AbortController;r.current=xe;let Se=!1;const te=L=>{if(Se||xe.signal.aborted||L.type!=="transcription")return;const $=L.normalized||L.data;(O.length===0||O.some(pe=>$.toLowerCase().includes(pe.toLowerCase())))&&(Se=!0,clearTimeout(oe),re(),de($))},re=ut.addEventListener(te),oe=setTimeout(()=>{Se||(Se=!0,xe.abort(),re(),Le(new Error("Timeout esperando respuesta específica")))},J);xe.signal.addEventListener("abort",()=>{Se||(Se=!0,clearTimeout(oe),re(),Le(new Error("Operation aborted")))})}),[]),ue=C.useCallback(O=>ut.addEventListener(O),[]),P=C.useCallback(O=>q(null,null,function*(){return yield Er(O)}),[]),Q=C.useCallback(()=>o,[o]),w=C.useCallback(()=>d,[d]),G={state:l,startListening:g,stopListening:b,clearTranscription:p,reset:S,simulateTranscription:R,validateGameResponse:H,waitForValidResponse:k,waitForCustomResponse:ee,addEventListener:ue,normalizeText:P,getSupportedResponses:Q,getResponseHelp:w,getConfidenceScore:Z,getAlternativeResponses:j,isResponseAmbiguous:V};return A.jsx(_2.Provider,{value:G,children:c})},qa={LANGUAGE:"es",BASE_URL:"https://dev.dl2discovery.org/sts/api/v1/",API_KEY:"************************************",CACHE_TTL:24*60*60*1e3,REQUEST_TIMEOUT:3e4,RETRY_DELAY:1e3},l2={RATE:1.1,OUTPUT_FORMAT:"mp3"};function i2(){console.log("✅ [Config] Configuración validada correctamente"),console.log("📍 BASE_URL:",qa.BASE_URL),console.log("🔑 API_KEY presente:",!0)}class v4{constructor(l=qa.CACHE_TTL){me(this,"cache",new Map);me(this,"ttl");this.ttl=l}getCacheKey(l,u){return`${u}-${l.toLowerCase().trim()}`}get(l,u){const r=this.getCacheKey(l,u),o=this.cache.get(r);return o&&Date.now()-o.timestamp<this.ttl?(console.log("🎯 Audio cache hit:",l.substring(0,30)),o.blob):(o&&this.cache.delete(r),null)}set(l,u,r){const o=this.getCacheKey(l,u);this.cache.set(o,{blob:r,timestamp:Date.now(),voiceId:u})}cleanup(){const l=Date.now();let u=0;for(const[r,o]of this.cache.entries())l-o.timestamp>this.ttl&&(this.cache.delete(r),u++);u>0&&console.log(`ℹ️ [AudioCache] 🧹 Cache limpiado: ${u} entradas removidas`)}clear(){this.cache.clear()}size(){return this.cache.size}getStats(){return{size:this.cache.size,ttl:this.ttl,hitRate:0}}}class b4{constructor(l=qa.RETRY_DELAY){me(this,"queue",[]);me(this,"isProcessing",!1);me(this,"retryDelay");this.retryDelay=l}handleThrottling(l){return q(this,null,function*(){var u;try{return yield l()}catch(r){if(((u=r.response)==null?void 0:u.status)===429)return console.warn("⚠️ [RetryQueue] ⚠️ Azure throttling detectado, encolando petición"),this.queueRequest(l);throw r}})}queueRequest(l){return q(this,null,function*(){return new Promise((u,r)=>{this.queue.push(()=>q(this,null,function*(){try{const o=yield l();u(o)}catch(o){r(o)}})),this.processQueue()})})}processQueue(){return q(this,null,function*(){if(!(this.isProcessing||this.queue.length===0)){for(this.isProcessing=!0,console.log(`ℹ️ [RetryQueue] 🔄 Procesando cola de ${this.queue.length} peticiones`);this.queue.length>0;){const l=this.queue.shift();try{yield l(),yield new Promise(u=>setTimeout(u,this.retryDelay))}catch(u){console.error("❌ [RetryQueue] ❌ Retry falló",u)}}this.isProcessing=!1,console.log("✅ [RetryQueue] ✅ Cola procesada completamente")}})}getStats(){return{length:this.queue.length,isProcessing:this.isProcessing}}clear(){this.queue=[],this.isProcessing=!1}}function C4(c){return c.replace(/([.!?])\s+/g,"$1 ").trim()}function S4(c){if(!c||typeof c!="string")return!1;const l=c.trim();return!(l.length===0||!/[a-zA-ZáéíóúñÁÉÍÓÚÑ0-9]/.test(l))}const fn=class fn{constructor(){me(this,"voiceId","");me(this,"genre","");me(this,"availableVoicesList",[]);me(this,"audioCache");me(this,"retryQueue");this.audioCache=new v4,this.retryQueue=new b4}static getInstance(){return fn.instance||(fn.instance=new fn),fn.instance}getCurrentVoiceId(){return this.voiceId}getAvailableVoicesList(){return this.availableVoicesList}setVoiceId(l){this.voiceId=l}setGenre(l){this.genre=l}reset(){console.log("ℹ️ [AzureVoicesService] 🔄 Reseteando servicio..."),this.voiceId="",this.genre="",this.availableVoicesList=[],this.audioCache.clear(),this.retryQueue.clear()}getHeaders(){return{Authorization:`Bearer ${qa.API_KEY}`}}getAvailableVoices(){return q(this,null,function*(){i2(),console.log("🔍 [AzureVoicesService] Obteniendo voces disponibles...");const l={language:qa.LANGUAGE,gender:this.genre};try{const u=yield t2(Ve.post(`${qa.BASE_URL}available_voices`,l,{headers:this.getHeaders()}));return console.log("✅ [AzureVoicesService] Voces obtenidas:",u),u}catch(u){throw console.error("❌ [AzureVoicesService] Error obteniendo voces:",u),u}})}configVoice(l){return q(this,null,function*(){this.setGenre(l);try{const u=yield this.getAvailableVoices();if(this.availableVoicesList=u,u.length>0){const r=u[Math.floor(Math.random()*u.length)];return this.setVoiceId(r),console.log(`✅ [AzureVoicesService] Voz configurada: ${r}`),!0}return console.warn("⚠️ [AzureVoicesService] No hay voces disponibles"),!1}catch(u){return console.error("❌ [AzureVoicesService] Error configurando voz",u),this.availableVoicesList=[],!1}})}getAudio(l){return q(this,null,function*(){if(i2(),!this.voiceId)throw new Error("Azure Speech no configurado correctamente");if(!S4(l))throw new Error("Texto no válido para síntesis");const u=this.audioCache.get(l,this.voiceId);if(u)return u;const r=C4(l);return console.log(`🔍 [AzureVoicesService] Generando audio para: "${r.substring(0,50)}..."`),this.retryQueue.handleThrottling(()=>q(this,null,function*(){const o={input_text:r,voice_params:{voice_id:this.voiceId,rate:l2.RATE},output_format:l2.OUTPUT_FORMAT};console.log("🔍 [AzureVoicesService] Usando formato correcto:",o);try{const d=yield t2(Ve.post(`${qa.BASE_URL}t2s`,o,{responseType:"blob",headers:this.getHeaders()}));return console.log(`✅ [AzureVoicesService] ¡ÉXITO! Audio generado (${d.size} bytes)`),this.audioCache.set(l,this.voiceId,d),d}catch(d){throw console.error("❌ [AzureVoicesService] Error generando audio:",d),d}}))})}cleanupCache(){this.audioCache.cleanup()}getPerformanceStats(){return{cache:this.audioCache.getStats(),queue:this.retryQueue.getStats(),voice:{configured:!!this.voiceId,voiceId:this.voiceId,availableCount:this.availableVoicesList.length}}}testAPIConnection(){return q(this,null,function*(){var u;console.log("🧪 [AzureVoicesService] Testing API connection...");const l={input_text:"Hola mundo",voice_params:{voice_id:this.voiceId||"Elvira",rate:1.1},output_format:"mp3"};try{const r=yield Ve.post(`${qa.BASE_URL}t2s`,l,{headers:this.getHeaders(),timeout:5e3,responseType:"blob"});console.log("✅ [AzureVoicesService] API conectada correctamente:",r.status)}catch(r){if(console.error("❌ [AzureVoicesService] Error de conexión:",r),Ve.isAxiosError(r)&&((u=r.response)==null?void 0:u.data)instanceof Blob){const o=yield r.response.data.text();console.error("🔴 Error response from API:",o)}}})}};me(fn,"instance");let wr=fn;class E4{constructor(){me(this,"azureService");me(this,"serviceName","speechService");this.azureService=wr.getInstance(),console.log(`🔍 [${this.serviceName}] 🎤 SpeechService inicializado (solo generación)`)}getCurrentVoiceId(){return this.azureService.getCurrentVoiceId()}getAvailableVoicesList(){return this.azureService.getAvailableVoicesList()}configVoice(l){return q(this,null,function*(){try{console.log(`ℹ️ [${this.serviceName}] 🔧 Configurando voz: ${l}`);const u=yield this.azureService.configVoice(l);if(u){const r=this.azureService.getCurrentVoiceId();console.log(`✅ [${this.serviceName}] ✅ Voz configurada: ${r}`)}else console.warn(`⚠️ [${this.serviceName}] ❌ No se pudo configurar la voz`);return u}catch(u){return console.error(`❌ [${this.serviceName}] Error configurando voz`,u),!1}})}getAudio(l){return q(this,null,function*(){try{console.log(`🔍 [${this.serviceName}] 🎤 Generando audio: ${l.substring(0,50)}...`);const u=yield this.azureService.getAudio(l);return console.log(`✅ [${this.serviceName}] ✅ Audio generado correctamente (${u.size} bytes)`),u}catch(u){throw console.error(`❌ [${this.serviceName}] Error obteniendo audio`,u),u}})}getSpeech(l){return q(this,null,function*(){try{console.warn(`⚠️ [${this.serviceName}] ⚠️ getSpeech() está deprecated, usar getAudio() + AudioManager`);const u=yield this.getAudio(l);return URL.createObjectURL(u)}catch(u){throw console.error(`❌ [${this.serviceName}] Error obteniendo URL de speech`,u),u}})}speak(l){return q(this,null,function*(){throw console.warn(`⚠️ [${this.serviceName}] ⚠️ speak() está deprecated, usar getAudio() + AudioManager.playSpeech()`),new Error("speak() está deprecated. Usar getAudio() + AudioManager.playSpeech()")})}toSpeech(l){return q(this,null,function*(){throw console.warn(`⚠️ [${this.serviceName}] ⚠️ toSpeech() está deprecated, usar AudioManager`),new Error("toSpeech() está deprecated. Usar AudioManager.playSpeech()")})}setSpeech(l){throw console.warn(`⚠️ [${this.serviceName}] ⚠️ setSpeech() está deprecated, usar AudioManager`),new Error("setSpeech() está deprecated. Usar AudioManager")}playSpeech(){return q(this,null,function*(){throw console.warn(`⚠️ [${this.serviceName}] ⚠️ playSpeech() está deprecated, usar AudioManager`),new Error("playSpeech() está deprecated. Usar AudioManager.playSpeech()")})}stopSpeech(){throw console.warn(`⚠️ [${this.serviceName}] ⚠️ stopSpeech() está deprecated, usar AudioManager`),new Error("stopSpeech() está deprecated. Usar AudioManager.stopSpeech()")}noSpeech(){throw console.warn(`⚠️ [${this.serviceName}] ⚠️ noSpeech() está deprecated, usar AudioManager`),new Error("noSpeech() está deprecated. Usar AudioManager.stopSpeech()")}generateAudioWithAutoConfig(l,u="female"){return q(this,null,function*(){try{if(!this.getCurrentVoiceId()&&(console.log(`ℹ️ [${this.serviceName}] 🔧 Auto-configurando voz...`),!(yield this.configVoice(u))))throw new Error("No se pudo configurar la voz automáticamente");return yield this.getAudio(l)}catch(r){throw console.error(`❌ [${this.serviceName}] Error en generateAudioWithAutoConfig`,r),r}})}speakWithAutoConfig(l,u="female"){return q(this,null,function*(){throw console.warn(`⚠️ [${this.serviceName}] ⚠️ speakWithAutoConfig() está deprecated, usar generateAudioWithAutoConfig() + AudioManager`),new Error("speakWithAutoConfig() está deprecated. Usar generateAudioWithAutoConfig() + AudioManager.playSpeech()")})}cleanup(){this.azureService.cleanupCache(),console.log(`ℹ️ [${this.serviceName}] 🧹 Servicio limpiado (solo cache de Azure)`)}}const sl=new E4,dn=class dn{constructor(){me(this,"musicAudio",null);me(this,"speechAudio",null);me(this,"serviceName","audioManager");me(this,"state",{music:{isPlaying:!1,volume:.3,currentTrack:null},speech:{isPlaying:!1,volume:.8},sfx:{volume:.6},masterVolume:1,isMuted:!1});me(this,"listeners",new Set);this.initializeAudioElements(),window.audioManager=this}static getInstance(){return dn.instance||(dn.instance=new dn),dn.instance}initializeAudioElements(){this.musicAudio=document.createElement("audio"),this.musicAudio.id="music-audio",this.musicAudio.loop=!0,this.musicAudio.preload="auto",this.musicAudio.volume=this.state.music.volume*this.state.masterVolume,document.body.appendChild(this.musicAudio),this.speechAudio=document.createElement("audio"),this.speechAudio.id="speech-audio",this.speechAudio.preload="metadata",this.speechAudio.volume=this.state.speech.volume*this.state.masterVolume,document.body.appendChild(this.speechAudio),this.setupEventListeners(),console.log(`✅ [${this.serviceName}] AudioManager inicializado correctamente`)}setupEventListeners(){this.musicAudio&&(this.musicAudio.addEventListener("play",()=>{this.updateState({music:ge(ne({},this.state.music),{isPlaying:!0})})}),this.musicAudio.addEventListener("pause",()=>{this.updateState({music:ge(ne({},this.state.music),{isPlaying:!1})})}),this.musicAudio.addEventListener("ended",()=>{this.updateState({music:ge(ne({},this.state.music),{isPlaying:!1})})})),this.speechAudio&&(this.speechAudio.addEventListener("play",()=>{this.updateState({speech:ge(ne({},this.state.speech),{isPlaying:!0})}),this.duckMusic(!0)}),this.speechAudio.addEventListener("pause",()=>{this.updateState({speech:ge(ne({},this.state.speech),{isPlaying:!1})}),this.duckMusic(!1)}),this.speechAudio.addEventListener("ended",()=>{this.updateState({speech:ge(ne({},this.state.speech),{isPlaying:!1})}),this.duckMusic(!1)}))}playMusic(l){return q(this,null,function*(){if(this.musicAudio)try{console.log(`ℹ️ [${this.serviceName}] 🎵 Reproduciendo música: ${l}`),this.musicAudio.src=l,this.musicAudio.currentTime=0,yield this.musicAudio.play(),this.updateState({music:ge(ne({},this.state.music),{currentTrack:l,isPlaying:!0})}),console.log(`✅ [${this.serviceName}] Música iniciada correctamente`)}catch(u){if(u instanceof Error&&u.name==="NotAllowedError")console.warn(`⚠️ [${this.serviceName}] ⚠️ Autoplay bloqueado - esperando interacción del usuario`),this.updateState({music:ge(ne({},this.state.music),{currentTrack:l,isPlaying:!1})}),this.setupAutoplayRetry();else throw console.error(`❌ [${this.serviceName}] Error reproduciendo música`,u),u}})}setupAutoplayRetry(){const l=()=>q(this,null,function*(){if(this.musicAudio&&this.state.music.currentTrack&&!this.state.music.isPlaying)try{yield this.musicAudio.play(),this.updateState({music:ge(ne({},this.state.music),{isPlaying:!0})}),console.log(`✅ [${this.serviceName}] ✅ Música iniciada tras interacción del usuario`)}catch(u){console.warn(`⚠️ [${this.serviceName}] ⚠️ Aún no se puede reproducir música`)}["click","touchstart","keydown"].forEach(u=>{document.removeEventListener(u,l)})});["click","touchstart","keydown"].forEach(u=>{document.addEventListener(u,l,{once:!0})})}pauseMusic(){this.musicAudio&&!this.musicAudio.paused&&(this.musicAudio.pause(),console.log(`ℹ️ [${this.serviceName}] 🎵 Música pausada`))}resumeMusic(){this.musicAudio&&this.musicAudio.paused&&this.state.music.currentTrack&&(this.musicAudio.play().catch(l=>{console.error(`❌ [${this.serviceName}] Error reanudando música`,l)}),console.log(`ℹ️ [${this.serviceName}] 🎵 Música reanudada`))}stopMusic(){this.musicAudio&&(this.musicAudio.pause(),this.musicAudio.currentTime=0,this.musicAudio.src="",this.updateState({music:ge(ne({},this.state.music),{isPlaying:!1,currentTrack:null})}),console.log(`ℹ️ [${this.serviceName}] 🎵 Música detenida`))}playSpeech(l){return q(this,null,function*(){if(this.speechAudio)try{const u=URL.createObjectURL(l);console.log(`ℹ️ [${this.serviceName}] 🗣️ Reproduciendo narración`),this.speechAudio.src=u,this.speechAudio.currentTime=0,yield this.speechAudio.play(),this.speechAudio.addEventListener("ended",()=>{URL.revokeObjectURL(u)},{once:!0}),console.log(`✅ [${this.serviceName}] Narración iniciada correctamente`)}catch(u){if(u instanceof Error&&u.name==="NotAllowedError")console.warn(`⚠️ [${this.serviceName}] ⚠️ Autoplay de narración bloqueado - esperando interacción`),this.setupSpeechAutoplayRetry(l);else throw console.error(`❌ [${this.serviceName}] Error reproduciendo narración`,u),u}})}setupSpeechAutoplayRetry(l){const u=()=>q(this,null,function*(){if(this.speechAudio)try{const r=URL.createObjectURL(l);this.speechAudio.src=r,this.speechAudio.currentTime=0,yield this.speechAudio.play(),this.speechAudio.addEventListener("ended",()=>{URL.revokeObjectURL(r)},{once:!0}),console.log(`✅ [${this.serviceName}] ✅ Narración iniciada tras interacción del usuario`)}catch(r){console.warn(`⚠️ [${this.serviceName}] ⚠️ Aún no se puede reproducir narración`)}["click","touchstart","keydown"].forEach(r=>{document.removeEventListener(r,u)})});["click","touchstart","keydown"].forEach(r=>{document.addEventListener(r,u,{once:!0})})}pauseSpeech(){if(!this.speechAudio){console.warn(`⚠️ [${this.serviceName}] No hay elemento de audio de speech disponible`);return}if(this.speechAudio.paused){console.log(`ℹ️ [${this.serviceName}] El audio ya está pausado`);return}if(!this.speechAudio.src||this.speechAudio.src===""){console.warn(`⚠️ [${this.serviceName}] No hay archivo de audio para pausar`);return}this.speechAudio.pause(),console.log(`ℹ️ [${this.serviceName}] 🗣️ Narración pausada en ${this.speechAudio.currentTime.toFixed(2)}s`)}resumeSpeech(){if(!this.speechAudio){console.warn(`⚠️ [${this.serviceName}] No hay elemento de audio de speech disponible`);return}if(!this.speechAudio.src||this.speechAudio.src===""){console.warn(`⚠️ [${this.serviceName}] No hay archivo de audio cargado para reanudar`);return}if(!this.speechAudio.paused){console.log(`ℹ️ [${this.serviceName}] El audio ya se está reproduciendo`);return}this.speechAudio.src.startsWith("blob:")?this.speechAudio.play().catch(l=>{console.error(`❌ [${this.serviceName}] Error reanudando narración - posible blob revocado:`,l),this.speechAudio.src="",this.updateState({speech:ge(ne({},this.state.speech),{isPlaying:!1})})}):this.speechAudio.play().catch(l=>{console.error(`❌ [${this.serviceName}] Error reanudando narración`,l)}),console.log(`ℹ️ [${this.serviceName}] 🗣️ Intentando reanudar narración`)}stopSpeech(){this.speechAudio&&(this.speechAudio.pause(),this.speechAudio.currentTime=0,this.speechAudio.src.startsWith("blob:")&&URL.revokeObjectURL(this.speechAudio.src),this.speechAudio.src="",this.updateState({speech:ge(ne({},this.state.speech),{isPlaying:!1})}),console.log(`ℹ️ [${this.serviceName}] 🗣️ Narración detenida`))}hasSpeechAudio(){return!!(this.speechAudio&&this.speechAudio.src&&this.speechAudio.src!=="")}isSpeechPaused(){return!!(this.speechAudio&&this.speechAudio.paused&&this.hasSpeechAudio())}getSpeechCurrentTime(){var l;return((l=this.speechAudio)==null?void 0:l.currentTime)||0}getSpeechDuration(){var l;return((l=this.speechAudio)==null?void 0:l.duration)||0}duckMusic(l){if(!this.musicAudio)return;const u=l?this.state.music.volume*.3*this.state.masterVolume:this.state.music.volume*this.state.masterVolume;this.fadeAudioVolume(this.musicAudio,u,500)}fadeAudioVolume(l,u,r){const o=l.volume,d=u-o,m=20,g=r/m,b=d/m;let p=0;const S=setInterval(()=>{p++,l.volume=Math.max(0,Math.min(1,o+b*p)),p>=m&&(clearInterval(S),l.volume=u)},g)}setMusicVolume(l){const u=Math.max(0,Math.min(1,l));this.updateState({music:ge(ne({},this.state.music),{volume:u})}),this.musicAudio&&(this.musicAudio.volume=u*this.state.masterVolume),console.log(`🔍 [${this.serviceName}] 🎵 Volumen música: ${u}`)}setSpeechVolume(l){const u=Math.max(0,Math.min(1,l));this.updateState({speech:ge(ne({},this.state.speech),{volume:u})}),this.speechAudio&&(this.speechAudio.volume=u*this.state.masterVolume),console.log(`🔍 [${this.serviceName}] 🗣️ Volumen narración: ${u}`)}setMasterVolume(l){const u=Math.max(0,Math.min(1,l));this.updateState({masterVolume:u}),this.musicAudio&&(this.musicAudio.volume=this.state.music.volume*u),this.speechAudio&&(this.speechAudio.volume=this.state.speech.volume*u),console.log(`🔍 [${this.serviceName}] 🔊 Volumen master: ${u}`)}toggleMute(){const l=!this.state.isMuted;return this.setMute(l),l}setMute(l){this.updateState({isMuted:l}),this.musicAudio&&(this.musicAudio.muted=l),this.speechAudio&&(this.speechAudio.muted=l),console.log(`ℹ️ [${this.serviceName}] 🔇 Audio ${l?"silenciado":"activado"}`)}pauseAll(){this.pauseMusic(),this.pauseSpeech(),console.log(`ℹ️ [${this.serviceName}] ⏸️ Todo el audio pausado`)}resumeAll(){this.resumeMusic(),this.resumeSpeech(),console.log(`ℹ️ [${this.serviceName}] ▶️ Todo el audio reanudado`)}stopAll(){this.stopMusic(),this.stopSpeech(),console.log(`ℹ️ [${this.serviceName}] ⏹️ Todo el audio detenido`)}updateState(l){this.state=ne(ne({},this.state),l),this.notifyListeners()}getState(){return ne({},this.state)}addListener(l){return this.listeners.add(l),l(this.getState()),()=>{this.listeners.delete(l)}}notifyListeners(){this.listeners.forEach(l=>{try{l(this.getState())}catch(u){console.error(`❌ [${this.serviceName}] Error en listener de audio`,u)}})}isPlaying(l){if(!l)return this.state.music.isPlaying||this.state.speech.isPlaying;switch(l){case"music":return this.state.music.isPlaying;case"speech":return this.state.speech.isPlaying;default:return!1}}getVolume(l){switch(l){case"music":return this.state.music.volume;case"speech":return this.state.speech.volume;case"sfx":return this.state.sfx.volume;default:return 0}}destroy(){this.stopAll(),this.musicAudio&&(document.body.removeChild(this.musicAudio),this.musicAudio=null),this.speechAudio&&(document.body.removeChild(this.speechAudio),this.speechAudio=null),this.listeners.clear(),console.log(`ℹ️ [${this.serviceName}] 💀 AudioManager destruido`)}};me(dn,"instance");let Mr=dn;const Fe=Mr.getInstance(),A4={error:"critical",validation:"critical",game_response:"high",victory:"high",instruction:"high",question:"medium",welcome:"medium",hint:"medium",info:"low",effect:"low"},il={critical:4,high:3,medium:2,low:1},hn=class hn{constructor(){me(this,"serviceName","speechCoordinator");me(this,"state",{isPlaying:!1,currentRequest:null,queue:[],channel:"auto",lastActivity:0});me(this,"listeners",new Set);me(this,"processingPromise",null);console.log(`ℹ️ [${this.serviceName}] 🎯 SpeechCoordinator inicializado`)}static getInstance(){return hn.instance||(hn.instance=new hn),hn.instance}updateState(l){this.state=ne(ne({},this.state),l),this.listeners.forEach(u=>u(this.state))}subscribe(l){return this.listeners.add(l),()=>this.listeners.delete(l)}getState(){return ne({},this.state)}speak(o){return q(this,arguments,function*(l,u="info",r={}){if(!l.trim()){console.warn(`⚠️ [${this.serviceName}] ⚠️ Texto vacío ignorado`);return}const d=r.priority||A4[u],m=r.channel||this.determineOptimalChannel(),g={id:`speech-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,text:l.trim(),priority:d,type:u,channel:m,timestamp:Date.now(),timeout:r.timeout||1e4,onStart:r.onStart,onComplete:r.onComplete,onError:r.onError};return console.log(`ℹ️ [${this.serviceName}] 📝 Nueva solicitud [${d}/${u}]: ${l.substring(0,50)}...`),this.enqueueRequest(g)})}interrupt(l="critical"){if(!this.state.currentRequest)return;const u=il[this.state.currentRequest.priority];il[l]>u&&(console.log(`ℹ️ [${this.serviceName}] ⏹️ Interrumpiendo speech de prioridad ${this.state.currentRequest.priority}`),this.stopCurrent())}clearQueue(){console.log(`ℹ️ [${this.serviceName}] 🧹 Limpiando cola de speech`),this.updateState({queue:[]})}stopAll(){console.log(`ℹ️ [${this.serviceName}] 🛑 Parando todo speech`),this.stopCurrent(),this.clearQueue()}enqueueRequest(l){return q(this,null,function*(){if(this.state.currentRequest){const o=il[this.state.currentRequest.priority];il[l.priority]>o&&(console.log(`ℹ️ [${this.serviceName}] 🔄 Interrumpiendo para prioridad mayor: ${l.priority}`),this.stopCurrent())}const u=[...this.state.queue],r=u.findIndex(o=>il[o.priority]<il[l.priority]);r===-1?u.push(l):u.splice(r,0,l),this.updateState({queue:u}),this.processingPromise||(this.processingPromise=this.processQueue(),yield this.processingPromise,this.processingPromise=null)})}processQueue(){return q(this,null,function*(){var l;for(;this.state.queue.length>0&&!this.state.isPlaying;){const u=this.state.queue.shift();this.updateState({queue:this.state.queue,currentRequest:u,isPlaying:!0,lastActivity:Date.now()});try{yield this.executeRequest(u)}catch(r){console.error(`❌ [${this.serviceName}] ❌ Error ejecutando speech: ${r}`),(l=u.onError)==null||l.call(u,r)}finally{this.updateState({isPlaying:!1,currentRequest:null})}}})}executeRequest(l){return q(this,null,function*(){var r,o;console.log(`ℹ️ [${this.serviceName}] 🎯 Ejecutando [${l.channel}/${l.priority}]: ${l.text.substring(0,50)}...`),(r=l.onStart)==null||r.call(l);const u=Date.now();try{switch(l.channel){case"mhc":yield this.executeMHCSpeech(l);break;case"web":yield this.executeWebSpeech(l);break;case"auto":yield this.executeAutoSpeech(l);break}const d=Date.now()-u;console.log(`✅ [${this.serviceName}] ✅ Speech completado en ${d}ms`),(o=l.onComplete)==null||o.call(l)}catch(d){throw console.error(`❌ [${this.serviceName}] ❌ Error en speech: ${d}`),d}})}executeMHCSpeech(l){return q(this,null,function*(){if(!ut.isMHCAvailable()){console.warn(`⚠️ [${this.serviceName}] ⚠️ MHC no disponible, fallback a web`),yield this.executeWebSpeech(l);return}ut.speakAura(l.text);const u=Math.max(2e3,l.text.length*60);yield new Promise(r=>setTimeout(r,u))})}executeWebSpeech(l){return q(this,null,function*(){var u,r,o;try{const d=yield sl.getAudio(l.text);yield Fe.playSpeech(d)}catch(d){console.warn(`⚠️ [${this.serviceName}] Azure TTS falló, usando modo silencioso:`,d),Ve.isAxiosError(d)&&((u=d.response)==null?void 0:u.status)===422&&(console.error(`🔴 [${this.serviceName}] Error 422 - Estructura de datos incorrecta para Azure TTS`),console.error(`🔴 [${this.serviceName}] Revisa la documentación de la API de Azure TTS`),console.error(`🔴 [${this.serviceName}] URL: ${(r=d.config)==null?void 0:r.url}`),console.error(`🔴 [${this.serviceName}] Payload enviado:`,(o=d.config)==null?void 0:o.data)),yield this.executeSilentSpeech(l)}})}executeSilentSpeech(l){return q(this,null,function*(){console.log(`ℹ️ [${this.serviceName}] 🔇 Ejecutando speech silencioso: ${l.text.substring(0,50)}...`);const u=l.text.split(" ").length,r=Math.max(1e3,u/150*60*1e3);yield new Promise(o=>setTimeout(o,r)),console.log(`✅ [${this.serviceName}] 🔇 Speech silencioso completado`)})}executeAutoSpeech(l){return q(this,null,function*(){["game_response","question","instruction","welcome"].includes(l.type)&&ut.isMHCAvailable()?yield this.executeMHCSpeech(l):yield this.executeWebSpeech(l)})}determineOptimalChannel(){return ut.isMHCAvailable()?"auto":"web"}stopCurrent(){this.state.currentRequest&&(Fe.pauseSpeech(),console.log(`ℹ️ [${this.serviceName}] ⏹️ Speech actual interrumpido`),this.updateState({isPlaying:!1,currentRequest:null}))}speakError(l){return q(this,null,function*(){return this.speak(l,"error",{priority:"critical"})})}speakValidation(l){return q(this,null,function*(){return this.speak(l,"validation",{priority:"critical"})})}speakGameResponse(l){return q(this,null,function*(){return this.speak(l,"game_response",{priority:"high"})})}speakGameQuestion(l){return q(this,null,function*(){return this.speak(l,"question",{priority:"medium"})})}speakWelcome(l){return q(this,null,function*(){return this.speak(l,"welcome",{priority:"medium"})})}speakHint(l){return q(this,null,function*(){return this.speak(l,"hint",{priority:"medium"})})}speakInfo(l){return q(this,null,function*(){return this.speak(l,"info",{priority:"low"})})}speakMHC(l,u="info"){return q(this,null,function*(){return this.speak(l,u,{channel:"mhc"})})}speakWeb(l,u="info"){return q(this,null,function*(){return this.speak(l,u,{channel:"web"})})}speakWithCallback(l,u,r){return q(this,null,function*(){return this.speak(l,u,{onComplete:r})})}isSpeaking(){return this.state.isPlaying}getCurrentSpeech(){return this.state.currentRequest}getQueueLength(){return this.state.queue.length}};me(hn,"instance");let Nr=hn;const Be=Nr.getInstance(),T4=c=>{switch(c){case"error":return"error";case"answer":return"game_response";case"victory":return"victory";case"question":return"question";case"hint":return"hint";case"system":return"info";case"guess":return"game_response";default:return"info"}},w4={batman:"male",superman:"male","iron man":"male",thor:"male","captain america":"male",hulk:"male",spiderman:"male","spider-man":"male",wolverine:"male",deadpool:"male",joker:"male","harry potter":"male",gandalf:"male",yoda:"male","darth vader":"male","luke skywalker":"male","indiana jones":"male","james bond":"male","sherlock holmes":"male","wonder woman":"female","black widow":"female",catwoman:"female","hermione granger":"female","leia organa":"female","princess leia":"female","katniss everdeen":"female",elsa:"female",anna:"female",moana:"female",mulan:"female",pocahontas:"female",ariel:"female",belle:"female",man:"male",boy:"male",guy:"male",king:"male",prince:"male",woman:"female",girl:"female",queen:"female",princess:"female"},U2=C.createContext(void 0),Vs=()=>{const c=C.useContext(U2);if(!c)throw new Error("useSpeechOutput must be used within SpeechOutputProvider");return c},M4=({children:c})=>{const[l,u]=C.useState({isReady:!1,isConfiguring:!1,playbackState:"idle",currentVoice:"",availableVoices:[],errorMessage:null,audioState:Fe.getState(),isMusicPlaying:!1,isSpeechPlaying:!1}),[r,o]=C.useState([]),[d,m]=C.useState([]),[g,b]=C.useState(!1);C.useEffect(()=>{console.log("ℹ️ [speechOutput] 🔊 Inicializando SpeechOutputProvider con AudioManager"),p();const K=Fe.addListener(W=>{u(Ee=>ge(ne({},Ee),{audioState:W,isMusicPlaying:W.music.isPlaying,isSpeechPlaying:W.speech.isPlaying}))});return()=>{console.log("ℹ️ [speechOutput] 🧹 Limpiando SpeechOutputProvider"),K(),J(),pe()}},[]);const p=C.useCallback(()=>{u(K=>ge(ne({},K),{currentVoice:sl.getCurrentVoiceId(),availableVoices:sl.getAvailableVoicesList(),isReady:!!sl.getCurrentVoiceId()}))},[]),S=C.useCallback((K,W)=>({id:`msg-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,text:K,type:W,timestamp:new Date,voice:l.currentVoice}),[l.currentVoice]),R=C.useCallback(K=>{o(W=>[K,...W.slice(0,49)])},[]),H=C.useCallback(()=>q(null,null,function*(){try{yield Fe.playMusic("/assets/sounds/sound.mp3"),console.log("✅ [speechOutput] 🎵 Música de fondo iniciada")}catch(K){throw console.error("❌ [speechOutput] Error iniciando música de fondo",K),K}}),[]),Z=C.useCallback(()=>{Fe.pauseMusic()},[]),j=C.useCallback(()=>{Fe.resumeMusic()},[]),V=C.useCallback(()=>{Fe.stopMusic()},[]),k=C.useCallback(K=>{Fe.setMusicVolume(K)},[]),ee=C.useCallback(()=>{Fe.pauseSpeech()},[]),ue=C.useCallback(()=>{Fe.resumeSpeech()},[]),P=C.useCallback(()=>{Fe.stopSpeech()},[]),Q=C.useCallback(K=>{Fe.setSpeechVolume(K)},[]),w=C.useCallback(()=>Fe.toggleMute(),[]),G=C.useCallback(()=>{Fe.pauseAll()},[]),O=C.useCallback(()=>{Fe.resumeAll()},[]),J=C.useCallback(()=>{Fe.stopAll()},[]),de=C.useCallback(K=>q(null,null,function*(){if(!K.trim()){console.warn("⚠️ [speechOutput] ⚠️ Texto vacío para speak()");return}u(W=>ge(ne({},W),{playbackState:"loading",errorMessage:null}));try{const W=Date.now(),Ee=S(K,"system");yield Be.speak(K,"info",{onStart:()=>{u(We=>ge(ne({},We),{playbackState:"playing"}))},onComplete:()=>{const We=Date.now()-W;Ee.duration=We,R(Ee),u(rt=>ge(ne({},rt),{playbackState:"idle"})),console.log("✅ [speechOutput] ✅ Speech completado",{duration:We})},onError:We=>{u(rt=>ge(ne({},rt),{playbackState:"error",errorMessage:We.message})),console.error("❌ [speechOutput] ❌ Error en speech",We)}})}catch(W){throw u(Ee=>ge(ne({},Ee),{playbackState:"error",errorMessage:W instanceof Error?W.message:"Error desconocido"})),console.error("❌ [speechOutput] ❌ Error en speak()",W),W}}),[S,R]),Le=C.useCallback(()=>{ee(),u(K=>ge(ne({},K),{playbackState:"paused"}))},[ee]),xe=C.useCallback(()=>{ue(),u(K=>ge(ne({},K),{playbackState:"playing"}))},[ue]),Se=C.useCallback(()=>{P(),u(K=>ge(ne({},K),{playbackState:"idle"}))},[P]),te=C.useCallback(K=>q(null,null,function*(){console.log(`ℹ️ [speechOutput] 🔧 Configurando voz: ${K}`),u(W=>ge(ne({},W),{isConfiguring:!0,errorMessage:null}));try{const W=yield sl.configVoice(K);if(W)p(),console.log(`✅ [speechOutput] ✅ Voz configurada: ${K}`);else throw new Error(`No se pudo configurar voz ${K}`);return W}catch(W){const Ee=W instanceof Error?W.message:"Error configurando voz";return console.error("❌ [speechOutput] ❌ Error configurando voz",W),u(We=>ge(ne({},We),{errorMessage:Ee})),!1}finally{u(W=>ge(ne({},W),{isConfiguring:!1}))}}),[p]),re=C.useCallback((K,W)=>q(null,null,function*(){let Ee=K;switch(W){case"question":Ee=K;break;case"answer":Ee=K;break;case"hint":Ee=`Te doy una pista: ${K}`;break;case"victory":Ee=`¡Excelente! ${K}`;break;case"error":Ee=`Lo siento, ${K}`;break;case"system":Ee=K;break}console.log(`ℹ️ [speechOutput] 🎮 Speaking game message [${W}]: ${Ee.substring(0,50)}...`);const We=S(Ee,W);R(We);const rt=T4(W);yield Be.speak(Ee,rt)}),[S,R]),oe=C.useCallback((K,W)=>q(null,null,function*(){let Ee=K;switch(W){case"excited":Ee=`¡${K}!`;break;case"thoughtful":Ee=`Hmm... ${K}`;break;case"confident":Ee=`Estoy segura: ${K}`;break;case"disappointed":Ee=`Oh... ${K}`;break}console.log(`ℹ️ [speechOutput] 😊 Speaking with emotion [${W}]: ${K.substring(0,50)}...`);const We=W==="excited"?"victory":"info";yield Be.speak(Ee,We)}),[]),L=C.useCallback(K=>{const W=K.toLowerCase();for(const[rt,rl]of Object.entries(w4))if(W.includes(rt))return rl;const Ee=["mr","señor","king","prince","man","boy"],We=["ms","mrs","señora","queen","princess","woman","girl"];return Ee.some(rt=>W.includes(rt))?"male":(We.some(rt=>W.includes(rt)),"female")},[]),$=C.useCallback(K=>q(null,null,function*(){const W=L(K);return console.log(`ℹ️ [speechOutput] 🎭 Configurando voz para personaje: ${K} → ${W}`),yield te(W)}),[L,te]),le=C.useCallback(K=>q(null,null,function*(){if(g){console.warn("⚠️ [speechOutput] ⚠️ Ya se está procesando una cola");return}console.log(`ℹ️ [speechOutput] 📋 Procesando cola de ${K.length} mensajes`),m(K),b(!0);try{for(const W of K){if(d.length===0)break;yield Be.speak(W,"info"),m(Ee=>Ee.slice(1)),yield new Promise(Ee=>setTimeout(Ee,200))}}catch(W){console.error("❌ [speechOutput] ❌ Error procesando cola",W)}finally{b(!1),m([])}}),[g,d]),pe=C.useCallback(()=>{console.log("ℹ️ [speechOutput] 🗑️ Limpiando cola de mensajes"),m([]),b(!1),Be.clearQueue()},[]),v=C.useCallback(()=>{console.log("ℹ️ [speechOutput] ⏭️ Saltando mensaje actual"),Be.interrupt("critical"),d.length>1?m(K=>K.slice(1)):pe(),u(K=>ge(ne({},K),{playbackState:"idle"}))},[d,pe]),Y=C.useCallback(()=>sl.getAvailableVoicesList(),[]),I=C.useCallback((K="Hola, esta es una prueba de voz")=>q(null,null,function*(){console.log("ℹ️ [speechOutput] 🧪 Probando voz actual"),yield de(K)}),[de]),F=C.useCallback(()=>{console.log("ℹ️ [speechOutput] 🔄 Reseteando SpeechOutput"),J(),pe(),u({isReady:!1,isConfiguring:!1,playbackState:"idle",currentVoice:"",availableVoices:[],errorMessage:null,audioState:Fe.getState(),isMusicPlaying:!1,isSpeechPlaying:!1}),o([])},[J,pe]),se=C.useCallback(()=>r[0]||null,[r]),Ne=C.useCallback(()=>q(null,null,function*(){const K=se();K?(console.log("ℹ️ [speechOutput] 🔁 Repitiendo último mensaje"),yield de(K.text)):console.warn("⚠️ [speechOutput] ⚠️ No hay mensajes para repetir")}),[se,de]),ve={state:l,speak:de,pause:Le,resume:xe,stop:Se,playBackgroundMusic:H,pauseMusic:Z,resumeMusic:j,stopMusic:V,setMusicVolume:k,pauseSpeech:ee,resumeSpeech:ue,stopSpeech:P,setSpeechVolume:Q,toggleMute:w,pauseAll:G,resumeAll:O,stopAll:J,configure:te,speakGameMessage:re,speakWithEmotion:oe,setCharacterVoice:$,getVoiceForCharacter:L,speakQueue:le,clearQueue:pe,skipCurrent:v,getAvailableVoices:Y,testVoice:I,reset:F,messageHistory:r,getLastMessage:se,replayLastMessage:Ne};return A.jsx(U2.Provider,{value:ve,children:c})},sn={baseURL:"https://dev.dl2discovery.org/llm-api/v1/",apiKey:"9dcd0147-11e2-4e9e-aaf3-05e1498ce828",presets:{aura:"mapp-Test akinator_claude37US",user:"mapp-Test akinator_claude37Bi",genCharBot:"mapp-gen-char-bot"}},s2={player_vs_ia:`
    IA adivina personaje pensado por el usuario.
  `,ia_vs_player:`
    **Descripción**

    Eres Enygma, un juego de adivinanza que piensa en un personaje reconocido y famoso del entretenimiento. El usuario tiene hasta 20 preguntas para descubrir quién es.
    Todas las respuestas de Enygma se deben entregar en formato JSON con la siguiente estructura:
    {
      "respuesta": "Sí" | "No" | "No lo sé",
      "pista": "<pista derivada de la pregunta>",
      "acertado": true | false
    }

    - respuesta: debe ser únicamente uno de los valores: "Sí", "No" o "No lo sé".
    - pista: breve pista o aclaración basada en la pregunta del usuario, sin revelar el personaje.
    - acertado: true si la última suposición fue correcta, false en caso contrario.


    **Instrucciones para el sistema**

    Eres “Enygma”, un juego de adivinanza de personajes del mundo del entretenimiento (actores, directores o personajes ficticios de cine, TV, literatura, etc.).


    **Cómo jugar**
    - Ya has pensado en un personaje reconocible y conocido.
    - El usuario tiene hasta 20 preguntas para adivinarlo.
    - Sólo puedes responder con:
      - “Sí”
      - “No”
      - “No lo sé”
    - NO des nombres, explicaciones largas ni pistas directas en la respuesta de juego.


    **Formato de salida (JSON)**
    Cada vez que el usuario pregunte, devuelve un objeto JSON con tres campos:
    1. 'respuesta': tu “Sí”, “No” o “No lo sé”.
    2. 'pista': una breve pista derivada de la pregunta (sin revelar el personaje).
    3. 'acertado': 'true' si la última suposición fue correcta, 'false' en caso contrario.


    **Inicio del juego**
    Cuando todo esté listo, responde exactamente (en texto plano):
    Ya estoy pensando en un personaje del mundo del entretenimiento. Puedes empezar a hacer preguntas. Solo responderé con “sí”, “no” o “no lo sé”.


    **Ejemplo de interacción**

    Usuario: ¿Es este personaje una mujer?
    Enygma:
    {
      "respuesta": "Sí",
      "pista": "Este personaje es de series o películas con temática fantástica.",
      "acertado": false
    }

    Usuario: ¿Es un personaje de cine?
    Enygma:
    {
      "respuesta": "No",
      "pista": "Este personaje es más conocido por su participación en televisión y literatura.",
      "acertado": false
    }

    Usuario: ¿Estoy pensando en Hermione Granger?
    Enygma:
    {
      "respuesta": "Sí",
      "pista": "",
      "acertado": true
    }


    **Repetir el juego**

    - Si el usuario acierta antes de las 20 preguntas, devuelve el JSON con acertado: true y, a continuación, ofrece repetir:
    ¿Quieres jugar otra vez? Puedo pensar en otro personaje si quieres.

    - Si el usuario no acierta tras 20 preguntas, responde (formato de texto plano):
    Has hecho 20 preguntas y no lo has adivinado. Yo estaba pensando en [nombre del personaje].
    ¿Quieres jugar otra vez? Puedo pensar en otro personaje si quieres.
      `},mn=class mn{constructor(){me(this,"sessionId",null);me(this,"serviceName","ai");me(this,"sesid","");me(this,"autoSpeechEnabled",!0)}static getInstance(){return mn.instance||(mn.instance=new mn),mn.instance}getSessionId(){return this.sessionId}isAutoSpeechEnabled(){return this.autoSpeechEnabled}getPresetForMode(l){switch(l){case"player_vs_ia":return sn.presets.user;case"ia_vs_player":return sn.presets.aura;default:return sn.presets.user}}getStoredCharacter(){try{return localStorage.getItem("enygma_generated_character")}catch(l){return console.warn(`⚠️ [${this.serviceName}] No se pudo leer localStorage:`,l),null}}getApiConfig(){return ne({},sn)}enableAutoSpeech(){this.autoSpeechEnabled=!0,console.log(`ℹ️ [${this.serviceName}] 🔊 Auto-speech habilitado`)}disableAutoSpeech(){this.autoSpeechEnabled=!1,console.log(`ℹ️ [${this.serviceName}] 🔇 Auto-speech deshabilitado`)}generateResponseSilent(l,u,r){return q(this,null,function*(){const o=this.autoSpeechEnabled;this.autoSpeechEnabled=!1;try{return yield this.generateResponse(l,u,r)}finally{this.autoSpeechEnabled=o}})}parseEnygmaResponse(l){try{const u=JSON.parse(l.trim());return u&&typeof u=="object"&&typeof u.respuesta=="string"&&typeof u.pista=="string"&&typeof u.acertado=="boolean"&&["Sí","No","No lo sé"].includes(u.respuesta)?(console.log(`✅ [${this.serviceName}] JSON parseado correctamente:`,u),u):(console.warn(`⚠️ [${this.serviceName}] JSON no tiene la estructura esperada:`,u),null)}catch(u){try{const r=l.match(/\{[\s\S]*\}/);if(r){const o=r[0],d=JSON.parse(o);if(d&&typeof d=="object"&&typeof d.respuesta=="string"&&typeof d.pista=="string"&&typeof d.acertado=="boolean"&&["Sí","No","No lo sé"].includes(d.respuesta))return console.log(`✅ [${this.serviceName}] JSON extraído y parseado:`,d),d}}catch(r){console.warn(`⚠️ [${this.serviceName}] No se pudo extraer JSON del texto:`,r)}return console.warn(`⚠️ [${this.serviceName}] Error parseando JSON:`,u),console.warn(`⚠️ [${this.serviceName}] Texto original:`,l),null}}narrateAIContent(l,u="response"){return q(this,null,function*(){if(!(!this.autoSpeechEnabled||!l.trim()))try{switch(console.log(`🎤 [${this.serviceName}] Narrando contenido AI [${u}]: ${l.substring(0,50)}...`),u){case"character":console.log(`ℹ️ [${this.serviceName}] Narrando personaje generado: ${l}`);break;case"welcome":yield Be.speakWelcome(l);break;case"response":default:yield Be.speakGameResponse(l);break}console.log(`✅ [${this.serviceName}] Contenido AI narrado exitosamente`)}catch(r){console.warn(`⚠️ [${this.serviceName}] Error narrando contenido AI:`,r)}})}makeRequest(l,u){return q(this,null,function*(){try{const r=`${sn.baseURL}${l}`,o={headers:{"Access-Control-Allow-Origin":"*","Content-Type":"application/json","X-Api-Key":sn.apiKey||"","X-MG-Ses":this.sesid,"X-Frame-Options":"SAMEORIGIN"}};u?(o.method="POST",o.body=JSON.stringify(u)):o.method="GET";const d=yield fetch(r,o);if(!d.ok)throw new Error(`HTTP error! status: ${d.status}`);return yield d.json()}catch(r){throw new Error("Error al conectar con la API de IA")}})}buildPayload(l,u,r){const o=this.getPresetForMode(u);let d=s2[u];return u==="ia_vs_player"&&r&&(d=`${s2[u]}

IMPORTANTE: El personaje en el que debes pensar es: ${r}

Responde siempre como si estuvieras pensando en este personaje específico.`),{id:{ses:this.sessionId||void 0,clt:"aura-game-client",corr:`game-${Date.now()}`},preset:o,query:l,prompt_params:{preamble:d},model_params:{max_tokens:u==="ia_vs_player"?50:200}}}generateResponse(l,u,r){return q(this,null,function*(){var o,d,m;console.log(`ℹ️ [${this.serviceName}] Generando respuesta para modo ${u}`,{query:l.substring(0,100),sessionId:this.sessionId});try{const g=this.buildPayload(l,u,r);console.log(`🔍 [${this.serviceName}] Payload construido`,{preset:g.preset,character:r||"none"});const b=yield this.makeRequest("generate",g);if(!b.ok)throw new Error(b.message||"Error en la respuesta de la IA");if(console.log(`✅ [${this.serviceName}] Respuesta recibida`,{data:b,output:b.output,sessionId:((o=b.id)==null?void 0:o.ses)||this.sessionId}),(d=b.id)!=null&&d.ses){const R=this.sessionId;this.sessionId=b.id.ses,console.log(`ℹ️ [${this.serviceName}] Session ID actualizado: ${R} → ${this.sessionId}`)}console.log(`✅ [${this.serviceName}] Respuesta generada exitosamente`,{outputLength:(m=b.output)==null?void 0:m.length,sessionId:this.sessionId});let p,S=b.output;return u==="ia_vs_player"&&b.output&&(p=this.parseEnygmaResponse(b.output)||void 0,p?(S=p.respuesta,console.log(`🎯 [${this.serviceName}] Respuesta JSON procesada:`,{respuesta:p.respuesta,pista:p.pista,acertado:p.acertado})):console.warn(`⚠️ [${this.serviceName}] No se pudo parsear JSON, usando texto completo`)),S&&this.narrateAIContent(S,"response").catch(R=>{console.warn(`⚠️ [${this.serviceName}] Error en auto-narración:`,R)}),{ok:!0,output:b.output,sessionId:this.sessionId||"",parsedResponse:p}}catch(g){return console.error(`❌ [${this.serviceName}] Error generando respuesta`,g),{ok:!1,output:"",sessionId:this.sessionId||"",error:g instanceof Error?g.message:"Error desconocido"}}})}resetSession(){return q(this,null,function*(){if(!this.sessionId)return!0;try{if((yield this.makeRequest(`reset/${this.sessionId}`)).ok)return this.sessionId=null,!0;throw new Error("Error al resetear sesión")}catch(l){return this.sessionId=null,!1}})}generateCharacter(){return q(this,null,function*(){var l;console.log(`🎭 [${this.serviceName}] Generando personaje con GENCHARBOT`);try{const u=`genchar-${Date.now()}`,r={id:{ses:this.sessionId||void 0,clt:"aura-game-client",corr:u},corrid:u,preset:sn.presets.genCharBot,query:"Dime un personaje",prompt_params:{preamble:""},model_params:{max_tokens:50}};console.log(`🔍 [${this.serviceName}] Generando personaje con preset:`,{preset:r.preset});const o=yield this.makeRequest("generate",r);if(!o.ok)throw new Error(o.message||"Error al generar personaje");let d=(l=o.output)==null?void 0:l.trim();if(console.log(`✅ [${this.serviceName}] Personaje generado:`,d),d){try{localStorage.setItem("enygma_generated_character",d),localStorage.setItem("enygma_character_timestamp",new Date().toISOString()),console.log(`💾 [${this.serviceName}] Personaje guardado en localStorage`)}catch(m){console.warn(`⚠️ [${this.serviceName}] No se pudo guardar en localStorage:`,m)}this.narrateAIContent(d,"character").catch(m=>{console.warn(`⚠️ [${this.serviceName}] Error narrando personaje generado:`,m)})}return d||null}catch(u){return console.error(`❌ [${this.serviceName}] Error generando personaje:`,u),null}})}startNewGame(l,u){return q(this,null,function*(){console.log(`🎮 [${this.serviceName}] Iniciando nuevo juego en modo: ${l}`,{character:u});try{this.sessionId&&(console.log(`🔄 [${this.serviceName}] Reseteando sesión anterior: ${this.sessionId}`),yield this.resetSession());const r=l==="player_vs_ia"?"Piensa en alguien... y empezamos cuando quieras.":"¡Hola! Estoy listo para adivinar lo que sea. ¿Ya tienes un personaje en mente?",o=yield this.generateResponse(r,l,u);return o.ok&&console.log(`✅ [${this.serviceName}] Juego iniciado exitosamente`,{mode:l,sessionId:o.sessionId,character:u||"none"}),o}catch(r){return null}})}clearSession(){this.sessionId=null}};me(mn,"instance");let Rr=mn;const pt=Rr.getInstance(),pn=class pn{constructor(){me(this,"serviceName","aiContentSpeech");me(this,"config",{enabled:!0,characterAnnouncement:!0,responseNarration:!0,welcomeMessages:!0});console.log(`ℹ️ [${this.serviceName}] 🎤 AI Content Speech Wrapper inicializado`)}static getInstance(){return pn.instance||(pn.instance=new pn),pn.instance}enableAllSpeech(){this.config.enabled=!0,pt.enableAutoSpeech(),console.log(`ℹ️ [${this.serviceName}] 🔊 Toda la narración AI habilitada`)}disableAllSpeech(){this.config.enabled=!1,pt.disableAutoSpeech(),console.log(`ℹ️ [${this.serviceName}] 🔇 Toda la narración AI deshabilitada`)}enableCharacterAnnouncements(){this.config.characterAnnouncement=!0,console.log(`ℹ️ [${this.serviceName}] 🎭 Anuncios de personajes habilitados`)}disableCharacterAnnouncements(){this.config.characterAnnouncement=!1,console.log(`ℹ️ [${this.serviceName}] 🎭 Anuncios de personajes deshabilitados`)}enableResponseNarration(){this.config.responseNarration=!0,console.log(`ℹ️ [${this.serviceName}] 💬 Narración de respuestas habilitada`)}disableResponseNarration(){this.config.responseNarration=!1,console.log(`ℹ️ [${this.serviceName}] 💬 Narración de respuestas deshabilitada`)}generateResponseWithSpeech(l,u,r){return q(this,null,function*(){return console.log(`ℹ️ [${this.serviceName}] 🤖 Generando respuesta con narración automática`),!this.config.enabled||!this.config.responseNarration?pt.generateResponseSilent(l,u,r):pt.generateResponse(l,u,r)})}generateCharacterWithAnnouncement(){return q(this,null,function*(){if(console.log(`ℹ️ [${this.serviceName}] 🎭 Generando personaje con anuncio automático`),!this.config.enabled||!this.config.characterAnnouncement){const l=pt.isAutoSpeechEnabled();pt.disableAutoSpeech();try{return yield pt.generateCharacter()}finally{l&&pt.enableAutoSpeech()}}return pt.generateCharacter()})}startNewGameWithSpeech(l,u){return q(this,null,function*(){if(console.log(`ℹ️ [${this.serviceName}] 🎮 Iniciando juego con configuración de speech`),!this.config.enabled||!this.config.welcomeMessages){const r=pt.isAutoSpeechEnabled();pt.disableAutoSpeech();try{return yield pt.startNewGame(l,u)}finally{r&&pt.enableAutoSpeech()}}return pt.startNewGame(l,u)})}isSpeechEnabled(){return this.config.enabled&&Be.getState().isPlaying!==void 0}getConfig(){return ne({},this.config)}testSpeechIntegration(){return q(this,null,function*(){try{return console.log(`🧪 [${this.serviceName}] Probando integración de speech...`),this.config.enabled?(yield Be.speakInfo("Prueba de integración AI-Speech"),console.log(`✅ [${this.serviceName}] Integración de speech funcionando correctamente`),!0):(console.log(`ℹ️ [${this.serviceName}] Speech deshabilitado en configuración`),!1)}catch(l){return console.error(`❌ [${this.serviceName}] Error en integración de speech:`,l),!1}})}resetToDefaults(){this.config={enabled:!0,characterAnnouncement:!0,responseNarration:!0,welcomeMessages:!0},pt.enableAutoSpeech(),console.log(`ℹ️ [${this.serviceName}] 🔄 Configuración restablecida a valores por defecto`)}};me(pn,"instance");let xr=pn;const mr=xr.getInstance(),H2=C.createContext(void 0),jr=()=>{const c=C.useContext(H2);if(!c)throw new Error("useEnygmaGame must be used within EnygmaGameProvider");return c},N4=({children:c})=>{const[l,u]=C.useState(null),r=(l==null?void 0:l.phase)||"setup",o=(l==null?void 0:l.playerRole)||"guesser",d=(l==null?void 0:l.phase)==="questioning"&&l.questionCount<l.maxQuestions&&l.playerRole==="guesser",m=(l==null?void 0:l.phase)==="questioning"||(l==null?void 0:l.phase)==="guessing",g=l?l.maxQuestions-l.questionCount:20,b=l?l.questionCount/l.maxQuestions*100:0,p=C.useCallback((Q,w)=>q(null,null,function*(){try{let G=w;if(Q==="ia_vs_player"){console.log("🎭 Generando personaje para modo ia_vs_player...");const J=yield mr.generateCharacterWithAnnouncement();J?(G=J,console.log(`✅ Personaje generado: ${J}`)):(console.warn("⚠️ No se pudo generar personaje, usando personaje por defecto"),G="un personaje misterioso")}const O={id:`game-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,mode:Q,phase:"questioning",startTime:new Date,questionCount:0,maxQuestions:20,currentCharacter:G,aiConfidence:0,messages:[],playerRole:Q==="player_vs_ia"?"answerer":"guesser"};u(O),console.log("✅ Partida iniciada exitosamente",{sessionId:O.id,playerRole:O.playerRole,character:G})}catch(G){throw console.error("❌ Error al iniciar partida",G),G}}),[]),S=C.useCallback((Q,w=!0)=>q(null,null,function*(){if(!l||!w&&!l||w&&!d)return;const G={id:`msg-${Date.now()}`,text:Q,sender:"user",timestamp:new Date,type:w?"question":"presentation",countsAsQuestion:w};try{const O=yield mr.generateResponseWithSpeech(Q,l.mode,l.currentCharacter);if(console.log("AI Response:",O),O.ok){const J=Q.toLowerCase().includes("es ")&&(Q.includes("?")||Q.toLowerCase().includes("es el")||Q.toLowerCase().includes("es la"));let de=!1,Le=!1,xe=O.output;O.parsedResponse?(console.log("🎯 Usando respuesta JSON parseada:",O.parsedResponse),xe=`${O.parsedResponse.respuesta}${O.parsedResponse.pista?` - ${O.parsedResponse.pista}`:""}`,J&&(de=O.parsedResponse.acertado,Le=!O.parsedResponse.acertado)):(de=O.output.toLowerCase().includes("¡correcto!")||O.output.toLowerCase().includes("correcto!")||O.output.toLowerCase().includes("estaba pensando en")||O.output.toLowerCase().includes("bien hecho")||O.output.toLowerCase().includes("has acertado")||O.output.toLowerCase().includes("acertaste"),Le=J&&(O.output.toLowerCase().includes("no,")||O.output.toLowerCase().includes("no es")||O.output.toLowerCase().includes("prueba con otro")||O.output.toLowerCase().includes("incorrecto")));let Se="answer",te=!1,re,oe=!1;J&&(Se="guess",de&&(te=!0,re="user",oe=!0));const L=ne({id:`msg-${Date.now()+1}`,text:xe,sender:"ai",timestamp:new Date,type:Se},O.parsedResponse&&{});u($=>{if(!$)return null;const le=w?$.questionCount+1:$.questionCount;let pe;te?pe="finished":le>=$.maxQuestions?pe="guessing":pe="questioning";const v=ge(ne({},$),{questionCount:le,phase:pe,messages:[...$.messages,G,L]});if(te&&(v.endTime=new Date,v.winner=re,v.wasCorrect=oe,J&&re==="user")){const Y=Q.match(/es\s+(.+?)\?/i);Y&&(v.finalGuess=Y[1].trim())}return v})}else throw new Error("Error al obtener respuesta de la IA")}catch(O){throw O}}),[l,d]),R=C.useCallback(Q=>q(null,null,function*(){yield S(Q,!1)}),[S]),H=C.useCallback(Q=>q(null,null,function*(){if(!l||l.playerRole!=="answerer")return;const w={yes:"Sí",no:"No",maybe:"Tal vez",unknown:"No lo sé"}[Q],G={id:`msg-${Date.now()}`,text:w,sender:"user",timestamp:new Date,type:"answer",validatedResponse:Q};try{const O=yield mr.generateResponseWithSpeech(w,l.mode,l.currentCharacter);if(O.ok){const J=O.output.toLowerCase().includes("creo que")||O.output.toLowerCase().includes("es ")||O.output.toLowerCase().includes("¿es "),de={yes:10,no:-5,maybe:2,unknown:0}[Q],Le=Math.max(0,Math.min(100,l.aiConfidence+de)),xe={id:`msg-${Date.now()+1}`,text:O.output,sender:"ai",timestamp:new Date,type:J?"guess":"question",confidence:Le};u(Se=>{if(!Se)return null;const te=Se.questionCount+1,re=J||te>=Se.maxQuestions?"guessing":"questioning";return ge(ne({},Se),{questionCount:te,phase:re,aiConfidence:Le,messages:[...Se.messages,G,xe]})})}}catch(O){throw O}}),[l]),Z=C.useCallback(Q=>q(null,null,function*(){if(!l)return!1;const w={id:`msg-${Date.now()}`,text:`¿Es ${Q}?`,sender:l.playerRole==="guesser"?"user":"ai",timestamp:new Date,type:"guess"};try{const G=l.aiConfidence/100,O=Math.random()<G,J=O?l.playerRole==="guesser"?"user":"ai":l.playerRole==="guesser"?"ai":"user";return u(de=>de?ge(ne({},de),{phase:"finished",endTime:new Date,messages:[...de.messages,w],winner:J,finalGuess:Q,wasCorrect:O}):null),O}catch(G){return!1}}),[l]),j=C.useCallback(Q=>{if(!l)return;const w={victory:"user",defeat:"ai",timeout:void 0,quit:void 0}[Q];u(G=>G?ge(ne({},G),{phase:"finished",endTime:new Date,winner:w}):null)},[l]),V=C.useCallback(()=>{if(!l)return{suggestedQuestions:[],categoryProgress:{person:!1,profession:!1,appearance:!1,era:!1,nationality:!1},likelyCharacters:[],confidenceLevel:0};const Q=l.messages,w={person:Q.some(O=>O.text.toLowerCase().includes("persona")||O.text.toLowerCase().includes("real")||O.text.toLowerCase().includes("fictici")),profession:Q.some(O=>O.text.toLowerCase().includes("actor")||O.text.toLowerCase().includes("trabajo")||O.text.toLowerCase().includes("profesión")),appearance:Q.some(O=>O.text.toLowerCase().includes("pelo")||O.text.toLowerCase().includes("alto")||O.text.toLowerCase().includes("apariencia")),era:Q.some(O=>O.text.toLowerCase().includes("vivo")||O.text.toLowerCase().includes("siglo")||O.text.toLowerCase().includes("época")),nationality:Q.some(O=>O.text.toLowerCase().includes("americano")||O.text.toLowerCase().includes("país")||O.text.toLowerCase().includes("nacionalidad"))},G=[];return w.person||G.push("¿Es una persona real?"),w.profession||G.push("¿Es actor o actriz?"),w.era||G.push("¿Está vivo actualmente?"),w.nationality||G.push("¿Es de Estados Unidos?"),w.appearance||G.push("¿Tiene el pelo oscuro?"),{suggestedQuestions:G,categoryProgress:w,likelyCharacters:[],confidenceLevel:l.aiConfidence}},[l]),k=C.useCallback(()=>l?l.playerRole==="answerer"?["Sí","No","Tal vez","No lo sé"]:V().suggestedQuestions.slice(0,3):[],[l,V]),ee=C.useCallback(Q=>{var w;if(!Q.trim())return{isValid:!1,suggestion:"Por favor, escribe algo"};if(Q.length>200)return{isValid:!1,suggestion:"El texto es muy largo. Sé más conciso."};if((l==null?void 0:l.playerRole)==="answerer"){const G=ma.validate(Q);if(G.type==="invalid"||G.confidence<.6)return{isValid:!1,suggestion:((w=G.suggestions)==null?void 0:w[0])||"Responde solo con: Sí, No, Tal vez, o No lo sé"}}return{isValid:!0}},[l]),ue=C.useCallback(()=>{if(!l)return null;const Q=V(),w=l.questionCount;return w<5?"Empieza con preguntas amplias para descartar categorías grandes":w>15?"¡Te quedan pocas preguntas! Es hora de hacer suposiciones específicas":Q.suggestedQuestions.length>0?`Considera preguntar: ${Q.suggestedQuestions[0]}`:l.aiConfidence>70?"La IA parece muy confiada. ¡Podrías arriesgarte con una suposición!":null},[l,V]),P={session:l,currentPhase:r,playerRole:o,startNewGame:p,askQuestion:S,askInitialMessage:R,respondToQuestion:H,makeGuess:Z,endGame:j,getGameInsights:V,getSuggestedResponses:k,canAskQuestion:d,canMakeGuess:m,questionsRemaining:g,gameProgress:b,validateUserInput:ee,getHint:ue};return A.jsx(H2.Provider,{value:P,children:c})},k2=()=>{var P;const[c,l]=C.useState(Be.getState());C.useEffect(()=>Be.subscribe(l),[]);const u=C.useCallback((Q,w="info")=>q(null,null,function*(){return Be.speak(Q,w)}),[]),r=C.useCallback(Q=>q(null,null,function*(){return Be.speakError(Q)}),[]),o=C.useCallback(Q=>q(null,null,function*(){return Be.speakValidation(Q)}),[]),d=C.useCallback(Q=>q(null,null,function*(){return Be.speakGameResponse(Q)}),[]),m=C.useCallback(Q=>q(null,null,function*(){return Be.speakGameQuestion(Q)}),[]),g=C.useCallback(Q=>q(null,null,function*(){return Be.speakWelcome(Q)}),[]),b=C.useCallback(Q=>q(null,null,function*(){return Be.speakHint(Q)}),[]),p=C.useCallback(Q=>q(null,null,function*(){return Be.speakInfo(Q)}),[]),S=C.useCallback(()=>{Be.interrupt("critical")},[]),R=C.useCallback(()=>{Be.clearQueue()},[]),H=C.useCallback(()=>{Be.stopAll()},[]),Z=C.useCallback((Q,w="info")=>q(null,null,function*(){return Be.speakMHC(Q,w)}),[]),j=C.useCallback((Q,w="info")=>q(null,null,function*(){return Be.speakWeb(Q,w)}),[]),V=C.useCallback((Q,w,G)=>q(null,null,function*(){return Be.speakWithCallback(Q,w,G)}),[]),k=c.isPlaying,ee=c.queue.length,ue=((P=c.currentRequest)==null?void 0:P.text)||null;return{state:c,isSpeaking:k,queueLength:ee,currentSpeech:ue,speak:u,speakError:r,speakValidation:o,speakGameResponse:d,speakGameQuestion:m,speakWelcome:g,speakHint:b,speakInfo:p,interrupt:S,clearQueue:R,stopAll:H,speakMHC:Z,speakWeb:j,speakWithCallback:V}},R4=()=>{const{speakGameResponse:c,speakGameQuestion:l,speakValidation:u,speakHint:r,speakWelcome:o,interrupt:d,isSpeaking:m,queueLength:g}=k2();return{speakResponse:c,speakQuestion:l,speakValidation:u,speakHint:r,speakWelcome:o,interrupt:d,isSpeaking:m,queueLength:g}},q2=C.createContext(void 0),B2=()=>{const c=C.useContext(q2);if(!c)throw new Error("useGameOrchestrator must be used within GameOrchestratorProvider");return c},x4=({children:c})=>{const l=jr(),u=g4(),r=Vs(),o=R4(),[d,m]=C.useState("idle"),[g,b]=C.useState(!1),[p,S]=C.useState(null),[R,H]=C.useState(0),[Z,j]=C.useState(null),V=d!=="idle"&&d!=="initializing",[k,ee]=C.useState(!1),ue=C.useCallback(()=>q(null,null,function*(){m("initializing"),H(0);try{console.log("ℹ️ [orchestrator] 🚀 Initializing Enygma app");try{console.log("ℹ️ [orchestrator] 🔧 Configuring speech output"),(yield r.configure("female"))||(console.warn("⚠️ [orchestrator] No se pudo configurar la voz, activando modo silencioso"),ee(!0)),H(25)}catch(te){console.warn("⚠️ [orchestrator] Error configurando voz, activando modo silencioso:",te),ee(!0),H(25)}try{console.log("ℹ️ [orchestrator] 🎤 Starting speech input"),u.startListening(),H(50)}catch(te){console.warn("⚠️ [orchestrator] Error iniciando entrada de voz:",te),H(50)}console.log("ℹ️ [orchestrator] 🤖 Testing AI connectivity"),H(75);try{console.log("ℹ️ [orchestrator] 👋 Playing welcome message"),yield o.speakWelcome("El velo del misterio se alza. Bienvenido a Enygma. ¿Estás listo para el desafío?"),console.log("✅ [orchestrator] ✅ Welcome message played")}catch(te){console.warn("⚠️ [orchestrator] Error reproduciendo mensaje de bienvenida:",te)}try{yield r.playBackgroundMusic(),console.log("✅ [orchestrator] 🎵 Background music started")}catch(te){console.warn("⚠️ [orchestrator] No se pudo iniciar música de fondo:",te)}H(100),m("waiting_for_user_choice"),console.log("✅ [orchestrator] ✅ App initialized successfully")}catch(te){console.error("❌ [orchestrator] Error crítico en inicialización:",te),H(100),m("waiting_for_user_choice"),S(te instanceof Error?te.message:"Unknown initialization error")}}),[o,u]),P=C.useCallback(te=>q(null,null,function*(){try{console.log(`ℹ️ [orchestrator] 🎮 Announcing game start: ${te}`);try{console.log("✅ [orchestrator] ✅ Game start announced")}catch(re){console.warn("⚠️ [orchestrator] Error anunciando inicio del juego:",re)}}catch(re){console.error("❌ [orchestrator] Error crítico anunciando inicio del juego:",re)}}),[o]),Q=C.useCallback((te,re)=>q(null,null,function*(){try{let oe="";te==="win"?oe=re?`¡Excelente! Adiviné correctamente. El personaje era ${re}. `:"¡Felicidades! Has adivinado correctamente. ":te==="lose"?oe="Se acabaron las preguntas. ":oe="Empate - ¡Buen juego! ",yield o.speakResponse(oe)}catch(oe){throw oe}}),[o]),w=C.useCallback(te=>q(null,null,function*(){m("initializing"),b(!0);try{yield l.startNewGame(te),yield P(te),m("game_active")}catch(re){S(re instanceof Error?re.message:"Failed to start game"),m("error")}finally{b(!1)}}),[l,P]),G=C.useCallback(te=>q(null,null,function*(){if(d!=="game_active"||g)return;b(!0),m("processing_response");const re=()=>q(null,null,function*(){var oe;try{const L=l.validateUserInput(te);if(!L.isValid){yield o.speakValidation(L.suggestion||"No entendí esa respuesta. Inténtalo de nuevo.");return}yield J(te),((oe=l.session)==null?void 0:oe.phase)==="finished"?yield de(l.session):m("game_active")}catch(L){yield o.speakValidation("Lo siento, hubo un error. ¿Puedes repetir?"),m("game_active")}});j(()=>re),yield re(),b(!1)}),[d,g,l,o]),O=C.useCallback((te,re)=>q(null,null,function*(){return yield o.speakQuestion(te),re&&re.length>0?yield u.waitForCustomResponse(re):(yield u.waitForValidResponse()).toString()}),[o,u]),J=C.useCallback(te=>q(null,null,function*(){if(l.session)if(l.playerRole==="guesser")yield l.askQuestion(te);else{const re=u.validateGameResponse(te);re!=="invalid"&&(yield l.respondToQuestion(re))}}),[l,o,u]),de=C.useCallback(te=>q(null,null,function*(){m("showing_results");try{let re="draw",oe;te.winner==="ai"?(re="win",oe=te.finalGuess):te.winner==="user"?(re="win",oe=te.currentCharacter):(re="lose",oe=te.finalGuess||te.currentCharacter),yield Q(re,oe)}catch(re){}}),[Q]),Le=C.useCallback(()=>q(null,null,function*(){try{u.stopListening(),u.clearTranscription(),yield o.speakWelcome("¡Gracias por jugar a Enygma! ¿Te gustaría jugar otra partida?"),m("waiting_for_user_choice")}catch(te){}}),[u,o]),xe=C.useCallback(()=>{S(null),m("waiting_for_user_choice")},[]),Se=C.useCallback(()=>q(null,null,function*(){if(Z)try{yield Z(),S(null)}catch(te){}}),[Z]);return C.useEffect(()=>{var re;return(re=u.addEventListener)==null?void 0:re.call(u,oe=>{oe.type==="transcription"&&oe.normalized&&d==="game_active"&&G(oe.normalized)})},[u,G,d]),A.jsx(q2.Provider,{value:{flowState:d,isProcessing:g,error:p,initializeApp:ue,startGameFlow:w,handleUserInteraction:G,endGameFlow:Le,speakAndWaitForResponse:O,processGameTurn:J,handleGameEnd:de,announceGameStart:P,announceGameEnd:Q,recoverFromError:xe,retryLastAction:Se,isReady:V,setupProgress:R,isSilentMode:k},children:c})},O4=()=>({getItem:u=>{try{return localStorage.getItem(u)}catch(r){return null}},setItem:(u,r)=>{try{localStorage.setItem(u,r)}catch(o){console.warn(`No se pudo guardar ${u} en localStorage:`,o)}}}),Rs={ANALYTICS_CONSENT:"enygma_analytics_consent",ANALYTICS_TIMESTAMP:"enygma_analytics_timestamp",AUDIO_ACTIVATED:"enygma_audio_activated"},c2={ACCEPTED:"accepted",REJECTED:"rejected"},z4=({onAudioActivated:c,onConsentGiven:l})=>{const[u,r]=C.useState(!1),[o,d]=C.useState(!1),{getItem:m,setItem:g}=O4(),{speakGameMessage:b,configure:p,state:{isReady:S}}=Vs();C.useEffect(()=>{if(!m(Rs.ANALYTICS_CONSENT)){const ee=setTimeout(()=>r(!0),1500);return()=>clearTimeout(ee)}},[m]);const R=C.useCallback(k=>{const ee=k?c2.ACCEPTED:c2.REJECTED;g(Rs.ANALYTICS_CONSENT,ee),g(Rs.ANALYTICS_TIMESTAMP,new Date().toISOString()),console.log(`🍪 Analíticas ${k?"aceptadas":"rechazadas"}`)},[g]),H=C.useCallback(()=>q(null,null,function*(){S||(yield p("female")),g(Rs.AUDIO_ACTIVATED,"true"),c==null||c()}),[S,p,b,c,g]),Z=C.useCallback(k=>q(null,null,function*(){d(!0);try{R(k),k?yield H():l==null||l(),r(!1)}catch(ee){console.error("❌ Error procesando consentimiento:",ee),r(!1)}finally{d(!1)}}),[R,H,l]),j=C.useCallback(()=>{Z(!1)},[Z]),V=C.useCallback(()=>{Z(!0)},[Z]);return u?A.jsxs(A.Fragment,{children:[A.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:9998,backdropFilter:"blur(2px)"},onClick:j,"aria-label":"Cerrar banner de consentimiento"}),A.jsxs("div",{style:{position:"fixed",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"90%",maxWidth:"480px",backgroundColor:"#1e293b",borderRadius:"12px",padding:"32px",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.3)",zIndex:9999,animation:"slideUp 0.5s ease-out",textAlign:"center"},onClick:k=>k.stopPropagation(),role:"dialog","aria-modal":"true","aria-labelledby":"cookie-title","aria-describedby":"cookie-description",children:[A.jsx("h2",{id:"cookie-title",style:{margin:"0 0 16px 0",fontSize:"24px",fontWeight:"600",color:"#ffffff",lineHeight:"1.3"},children:"Ayúdanos a mejorar"}),A.jsx("p",{id:"cookie-description",style:{margin:"0 0 32px 0",fontSize:"16px",color:"#94a3b8",lineHeight:"1.5",maxWidth:"400px",marginLeft:"auto",marginRight:"auto"},children:"Al aceptar nos ayudas a mejorar la experiencia permitiendo el análisis estadístico de datos de uso."}),A.jsxs("div",{style:{display:"flex",gap:"16px",justifyContent:"center",flexDirection:window.innerWidth<480?"column":"row"},children:[A.jsx("button",{onClick:j,disabled:o,"aria-label":"Rechazar cookies analíticas",style:{minWidth:"120px",backgroundColor:"transparent",color:"#ffffff",border:"2px solid #475569",borderRadius:"6px",padding:"12px 24px",fontSize:"16px",fontWeight:"500",cursor:o?"not-allowed":"pointer",transition:"all 0.2s ease",opacity:o?.5:1},children:"Rechazar"}),A.jsx("button",{onClick:V,disabled:o,"aria-label":"Aceptar cookies analíticas y activar audio",style:{minWidth:"120px",backgroundColor:o?"#1e40af":"#2563eb",color:"#ffffff",border:"none",borderRadius:"6px",padding:"12px 24px",fontSize:"16px",fontWeight:"500",cursor:o?"not-allowed":"pointer",transition:"background-color 0.2s ease",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px"},children:o?A.jsxs(A.Fragment,{children:[A.jsx("div",{style:{width:"16px",height:"16px",border:"2px solid #ffffff30",borderTop:"2px solid #ffffff",borderRadius:"50%",animation:"spin 1s linear infinite"}}),"Activando..."]}):"Aceptar"})]})]}),A.jsx("style",{children:`
        @keyframes slideUp {
          from {
            transform: translate(-50%, -50%) translateY(100%);
            opacity: 0;
          }
          to {
            transform: translate(-50%, -50%) translateY(0);
            opacity: 1;
          }
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `})]}):null};var _r=function(c){var l=c.size,u=l===void 0?"large":l,r=c.color,o=c.className,d=c.id,m=u==="small"?2:u==="medium"?4:5;return X.createElement("div",{role:"status","aria-live":"polite","aria-label":"Cargando",className:"spinner ".concat(o!=null?o:""," ").concat(u),id:d,style:r?{border:"".concat(m,"px solid transparent"),borderTop:"".concat(m,"px solid ").concat(r)}:{}},X.createElement("span",{className:"sr-only"},"Cargando"))},L4=function(c){var l=c.onClick,u=c.isDisabled,r=u===void 0?!1:u,o=c.isLoading,d=o===void 0?!1:o,m=c.text,g=c.leftIcon,b=c.backgroundColor,p=c.textColor,S=c.borderRadius,R=c.className,H=c.ariaLabel,Z=c.id,j=C.useState(!1),V=j[0],k=j[1],ee=function(){!r&&!d&&(k(!0),l(),setTimeout(function(){return k(!1)},200))};return X.createElement("button",{type:"button",onClick:ee,className:"".concat(R!=null?R:""," button-secondary label1 ").concat(r?"disabled":""," ").concat(d?"loading":""," ").concat(V?" clicked":""),disabled:r,style:{backgroundColor:b,color:p,borderRadius:S},"aria-label":H,id:Z},X.createElement("span",{className:"button-content",style:{opacity:d?0:1}},g,m),d&&X.createElement("div",{className:"button-spinner-wrapper"},X.createElement(_r,{color:p,size:"small"})))},D4=function(c){var l=c.type,u=c.color,r={play:X.createElement(j4,{color:u}),pause:X.createElement(_4,{color:u}),musicOn:X.createElement(U4,{color:u}),musicOff:X.createElement(H4,{color:u}),soundOn:X.createElement(k4,{color:u}),soundOff:X.createElement(q4,{color:u}),aura:X.createElement(Y4,{color:u}),reload:X.createElement(G4,{color:u}),mic:X.createElement(V4,{color:u}),menu:X.createElement(B4,{color:u}),thumbUp:X.createElement(Q4,{color:u}),thumbDown:X.createElement(X4,{color:u}),thumbUpFilled:X.createElement(Z4,{color:u}),thumbDownFilled:X.createElement($4,{color:u}),close:X.createElement(J4,{color:u}),addMore:X.createElement(K4,{color:u}),back:X.createElement(F4,{color:u}),star:X.createElement(P4,{color:u}),starFilled:X.createElement(I4,{color:u}),next:X.createElement(W4,{color:u}),checkCircle:X.createElement(e3,{color:u})};return r[l]||null},j4=function(c){var l=c.color;return X.createElement("svg",{className:"icon play",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",fill:l},X.createElement("path",{d:"M6.00018 4.97066C6.00018 4.3186 6.70452 3.90993 7.27069 4.23336L19.5724 11.2627C20.1429 11.5887 20.1429 12.4112 19.5724 12.7373L7.27069 19.7666C6.70451 20.0901 6.00018 19.6814 6.00018 19.0293V4.97066Z",fill:l}))},_4=function(c){var l=c.color;return X.createElement("svg",{className:"icon pause",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",fill:l},X.createElement("path",{d:"M7.33566 4C8.1946 4.00006 8.89112 4.71617 8.89133 5.59961V18.4004C8.89112 19.2838 8.1946 19.9999 7.33566 20C6.47668 20 5.7802 19.2839 5.78 18.4004V5.59961C5.7802 4.71613 6.47668 4 7.33566 4ZM16.6687 4C17.5277 4 18.2241 4.71613 18.2243 5.59961V18.4004C18.2241 19.2839 17.5277 20 16.6687 20C15.8098 19.9999 15.1132 19.2838 15.113 18.4004V5.59961C15.1132 4.71621 15.8098 4.00012 16.6687 4Z",fill:l}))},U4=function(c){var l=c.color;return X.createElement("svg",{className:"icon music",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 -960 960 960",fill:l},X.createElement("path",{d:"M400-120q-66 0-113-47t-47-113q0-66 47-113t113-47q23 0 42.5 5.5T480-418v-422h240v160H560v400q0 66-47 113t-113 47Z",fill:l}))},H4=function(c){var l=c.color;return X.createElement("svg",{className:"icon musicOff",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 -960 960 960",fill:l},X.createElement("path",{d:"M792-56 56-792l56-56 736 736-56 56ZM560-514l-80-80v-246h240v160H560v166ZM400-120q-66 0-113-47t-47-113q0-66 47-113t113-47q23 0 42.5 5.5T480-418v-62l80 80v120q0 66-47 113t-113 47Z",fill:l}))},k4=function(c){var l=c.color;return X.createElement("svg",{className:"icon soundOn",viewBox:"0 0 32 32",fill:l},X.createElement("path",{d:"M17.6616 5.15217C17.3328 4.95376 16.9337 4.95002 16.5975 5.13719L6.98687 10.5915H4.06045C3.47665 10.5915 3 11.0745 3 11.6659V19.3365C3 19.928 3.47665 20.4109 4.06045 20.4109H6.98687L16.5679 25.8465C16.7379 25.9476 16.9263 26 17.1185 26C17.3106 26 17.4769 25.9551 17.6395 25.8615C17.9794 25.6706 18.19 25.3074 18.19 24.9106V6.08805C18.19 5.69873 17.9942 5.35058 17.6616 5.15217Z",fill:l}),X.createElement("path",{d:"M22.1973 21.3529C21.9701 21.3529 21.7467 21.2528 21.5966 21.0641C21.3309 20.7329 21.3848 20.2477 21.7159 19.9821C22.9944 18.9578 23.726 17.4329 23.726 15.8002C23.726 14.1675 22.9944 12.6426 21.7159 11.6183C21.3848 11.3526 21.3309 10.8674 21.5966 10.5363C21.8623 10.2051 22.3475 10.1512 22.6786 10.4169C24.3229 11.7338 25.2663 13.6977 25.2663 15.8002C25.2663 17.9027 24.3229 19.8665 22.6786 21.1835C22.5361 21.299 22.3667 21.3529 22.1973 21.3529Z",fill:l}),X.createElement("path",{d:"M24.2637 24.826C24.0365 24.826 23.8132 24.7258 23.663 24.5372C23.3973 24.206 23.4512 23.7208 23.7823 23.4551C26.1197 21.5798 27.4598 18.7919 27.4598 15.7999C27.4598 12.8079 26.1197 10.0161 23.7823 8.14467C23.4512 7.87897 23.3973 7.39378 23.663 7.06262C23.9287 6.73146 24.4139 6.67755 24.745 6.94325C27.4482 9.1112 29.0001 12.3381 29.0001 15.7999C29.0001 19.2617 27.4482 22.4886 24.745 24.6565C24.6026 24.772 24.4331 24.826 24.2637 24.826Z",fill:l}))},q4=function(c){var l=c.color;return X.createElement("svg",{className:"icon soundOff",viewBox:"0 0 32 32",fill:l},X.createElement("path",{d:"M17.6613 5.15217C17.3325 4.95376 16.9334 4.95002 16.5972 5.13719L6.98678 10.5915H4.06043C3.47664 10.5915 3 11.0745 3 11.6659V19.3365C3 19.928 3.47664 20.4109 4.06043 20.4109H6.98678L16.5676 25.8465C16.7376 25.9476 16.926 26 17.1182 26C17.3103 26 17.4766 25.9551 17.6391 25.8615C17.9791 25.6706 18.1897 25.3074 18.1897 24.9106V6.08805C18.1897 5.69873 17.9939 5.35058 17.6613 5.15217Z",fill:l}),X.createElement("path",{d:"M26.9011 15.4998L30.3447 12.0108C30.6292 11.7225 30.6292 11.2583 30.3447 10.9701C30.0602 10.6818 29.602 10.6818 29.3175 10.9701L25.8739 14.4591L22.4302 10.9701C22.1457 10.6818 21.6876 10.6818 21.4031 10.9701C21.1186 11.2583 21.1186 11.7225 21.4031 12.0108L24.8467 15.4998L21.4031 18.9888C21.1186 19.277 21.1186 19.7412 21.4031 20.0295C21.5435 20.1717 21.7319 20.2466 21.9167 20.2466C22.1014 20.2466 22.2898 20.1755 22.4302 20.0295L25.8739 16.5405L29.3175 20.0295C29.4579 20.1717 29.6464 20.2466 29.8311 20.2466C30.0158 20.2466 30.2043 20.1755 30.3447 20.0295C30.6292 19.7412 30.6292 19.277 30.3447 18.9888Z",fill:l}))},B4=function(c){var l=c.color;return X.createElement("svg",{className:"icon menu",viewBox:"0 0 32 32",fill:l},X.createElement("path",{d:"M17.9998 8C17.9998 9.10457 17.1043 10 15.9998 10C14.8952 10 13.9998 9.10457 13.9998 8C13.9998 6.89543 14.8952 6 15.9998 6C17.1043 6 17.9998 6.89543 17.9998 8Z",fill:l}),X.createElement("path",{d:"M17.9998 16C17.9998 17.1046 17.1043 18 15.9998 18C14.8952 18 13.9998 17.1046 13.9998 16C13.9998 14.8954 14.8952 14 15.9998 14C17.1043 14 17.9998 14.8954 17.9998 16Z",fill:l}),X.createElement("path",{d:"M15.9998 26C17.1043 26 17.9998 25.1046 17.9998 24C17.9998 22.8954 17.1043 22 15.9998 22C14.8952 22 13.9998 22.8954 13.9998 24C13.9998 25.1046 14.8952 26 15.9998 26Z",fill:l}))},V4=function(c){var l=c.color;return X.createElement("svg",{className:"icon mic",viewBox:"0 0 32 32",fill:l},X.createElement("path",{d:"M21.7638 8.70502C21.7638 4.94404 19.7171 2.87122 15.9972 2.87122C12.2774 2.87122 10.2269 4.94404 10.2269 8.70502V17.7582C10.2269 21.5192 12.2736 23.592 15.9935 23.592C19.7171 23.592 21.7638 21.5192 21.7638 17.7582V8.70502ZM17.3007 27.8311C17.3007 27.1177 16.7293 26.5426 16.0271 26.5426C15.325 26.5426 14.7535 27.1177 14.7535 27.8311C14.7498 28.5369 15.3212 29.1158 16.0271 29.1196C16.7293 29.1196 17.3007 28.5407 17.3007 27.8311ZM20.4641 25.254C21.1662 25.254 21.7377 25.8292 21.7377 26.5426C21.7377 27.2522 21.1662 27.8311 20.4641 27.8311C19.7582 27.8273 19.1868 27.2522 19.1905 26.5426C19.1905 25.8292 19.7619 25.254 20.4641 25.254ZM25.3007 23.4725C25.3007 22.7592 24.7293 22.184 24.0271 22.184C23.3212 22.184 22.7535 22.7629 22.7535 23.4725C22.7498 24.1784 23.3212 24.7573 24.0271 24.761C24.7293 24.761 25.3007 24.1821 25.3007 23.4725ZM25.3007 17.7993C25.9057 17.8031 26.3913 18.2961 26.3875 18.8974C26.3913 19.5024 25.9057 19.9954 25.3007 19.9991C24.6957 19.9954 24.2101 19.5024 24.2139 18.8974C24.2101 18.2923 24.6957 17.8031 25.3007 17.7993ZM12.804 26.5426C12.804 25.8292 12.2325 25.254 11.5304 25.254C10.8245 25.254 10.2531 25.8292 10.2568 26.5426C10.2531 27.2522 10.8245 27.8273 11.5304 27.8311C12.2325 27.8311 12.804 27.2522 12.804 26.5426ZM7.96737 22.184C8.66951 22.184 9.24094 22.7592 9.24094 23.4725C9.24094 24.1821 8.66951 24.761 7.96737 24.761C7.26148 24.7573 6.69005 24.1784 6.69379 23.4725C6.69379 22.7629 7.26148 22.184 7.96737 22.184ZM6.69379 19.9991C7.29883 19.9954 7.78436 19.5024 7.78062 18.8974C7.78436 18.2923 7.29883 17.8031 6.69379 17.7993C6.08875 17.8031 5.60322 18.2923 5.60695 18.8974C5.60322 19.5024 6.08875 19.9954 6.69379 19.9991Z",fill:l}))},G4=function(c){var l=c.color;return X.createElement("svg",{className:"icon reload",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M7.11247 8.67596C7.16567 9.0531 6.91098 9.39871 6.54997 9.45428C6.51967 9.46216 6.48944 9.4621 6.45914 9.4621C6.13629 9.4621 5.85164 9.21248 5.80582 8.86737L5.60368 7.39764C4.71443 8.73947 4.22868 10.3399 4.22868 11.9953C4.22874 16.4733 7.71357 20.1135 12.0002 20.1135C13.178 20.1134 14.31 19.8513 15.3625 19.3195C15.6854 19.1608 16.0852 19.3035 16.2297 19.6408C16.3893 19.9862 16.2517 20.3959 15.9211 20.5666C14.6938 21.1857 13.3677 21.4992 11.9923 21.4992C6.97609 21.4992 2.89863 17.2394 2.8986 11.9992C2.8986 10.0937 3.44512 8.26344 4.4445 6.70331L3.25211 6.88202C2.89109 6.9376 2.5602 6.67127 2.507 6.29413C2.45394 5.91708 2.70856 5.56747 3.0695 5.5119L6.60368 4.98065L7.11247 8.67596ZM12.0002 2.49921C17.0162 2.49946 21.0939 6.7591 21.0939 11.9992C21.0939 13.9127 20.5543 15.7351 19.5548 17.2873L20.7482 17.1086C21.1013 17.0612 21.4389 17.3188 21.4923 17.6955C21.5456 18.0647 21.2911 18.4182 20.9377 18.4777L17.4074 19.0099L16.8986 15.3137C16.8456 14.9366 17.1002 14.5919 17.4611 14.5363C17.8143 14.489 18.1519 14.7464 18.2052 15.1232L18.4064 16.592C19.288 15.2502 19.7746 13.6665 19.7746 12.0031C19.7746 7.52508 16.2897 3.88495 12.0031 3.88495C10.8404 3.88501 9.71522 4.14725 8.67789 4.66327C8.34738 4.82164 7.94506 4.67837 7.79313 4.33319C7.64112 3.98781 7.77794 3.56718 8.10856 3.40839C9.33218 2.80502 10.6397 2.49921 12.0002 2.49921Z",fill:l}))},Y4=function(c){var l=c.color;return X.createElement("svg",{className:"icon aura",viewBox:"0 0 32 32",fill:l},X.createElement("path",{d:"M14.9316 26.6527C15.7404 26.0522 16.8726 26.2352 17.4605 27.0615C18.0484 27.8878 17.8692 29.0445 17.0605 29.645C16.2516 30.2455 15.1194 30.0625 14.5316 29.2363C13.9437 28.41 14.1228 27.2533 14.9316 26.6527ZM7.56143 26.3332C7.89716 25.861 8.5441 25.7562 9.00639 26.0992C9.46868 26.4422 9.57124 27.1031 9.23551 27.5754C8.89979 28.0476 8.25284 28.1524 7.79055 27.8094C7.32827 27.4664 7.22571 26.8055 7.56143 26.3332ZM22.9921 26.0994C23.4543 25.7562 24.1012 25.8608 24.4371 26.3329C24.7731 26.8051 24.6707 27.466 24.2085 27.8092C23.7463 28.1524 23.0994 28.0478 22.7634 27.5757C22.4275 27.1035 22.5299 26.4426 22.9921 26.0994ZM10.1075 20.0586C10.9469 18.8779 12.5641 18.6159 13.7199 19.4734C14.8757 20.3309 15.1321 21.9832 14.2927 23.164C13.4534 24.3446 11.836 24.6067 10.6802 23.7491C9.52455 22.8915 9.26807 21.2393 10.1075 20.0586ZM18.2805 19.4722C19.4359 18.6142 21.0533 18.8756 21.8931 20.0561C22.7329 21.2365 22.4771 22.8889 21.3216 23.7468C20.1662 24.6048 18.5487 24.3434 17.7089 23.1629C16.8691 21.9826 17.125 20.3301 18.2805 19.4722ZM26.5004 18.1826C27.4512 17.8668 28.4727 18.3983 28.7819 19.3697C29.091 20.3411 28.5707 21.3846 27.6199 21.7005C26.669 22.0163 25.6476 21.4849 25.3385 20.5134C25.0293 19.5419 25.5495 18.4985 26.5004 18.1826ZM3.47172 18.8482C4.05929 18.0217 5.19145 17.8383 6.00043 18.4385C6.8094 19.0388 6.98892 20.1954 6.40136 21.0219C5.81379 21.8484 4.68174 22.0318 3.87266 21.4315C3.06368 20.8313 2.88416 19.6746 3.47172 18.8482ZM7.38978 13.4105C7.83092 12.0227 9.28991 11.2629 10.6484 11.7136C12.0069 12.1643 12.7506 13.6548 12.3095 15.0427C11.8683 16.4305 10.4094 17.1904 9.05091 16.7396C7.69231 16.2889 6.94863 14.7985 7.38978 13.4105ZM21.3485 11.711C22.707 11.2598 24.1661 12.0191 24.6078 13.4068C25.0494 14.7946 24.3062 16.2853 22.9478 16.7365C21.5894 17.1877 20.1302 16.4284 19.6886 15.0407C19.247 13.6529 19.9901 12.1622 21.3485 11.711ZM2.71687 11.8576C2.89333 11.3024 3.47694 10.9985 4.02034 11.1788C4.56375 11.3591 4.86124 11.9552 4.68478 12.5103C4.50832 13.0656 3.92471 13.3695 3.3813 13.1891C2.8379 13.0089 2.54041 12.4127 2.71687 11.8576ZM27.978 11.1788C28.5214 10.9984 29.1051 11.3021 29.2817 11.8572C29.4584 12.4123 29.1612 13.0086 28.6178 13.1891C28.0744 13.3696 27.4907 13.0658 27.3141 12.5108C27.1374 11.9556 27.4347 11.3594 27.978 11.1788ZM15.9974 7.01869C17.4258 7.01839 18.584 8.20116 18.5842 9.6604C18.5844 11.1197 17.4268 12.3028 15.9984 12.3031C14.57 12.3033 13.4118 11.1205 13.4116 9.66131C13.4114 8.20207 14.5691 7.019 15.9974 7.01869ZM22.8383 4.80574C23.8382 4.80553 24.649 5.63351 24.6491 6.65499C24.6493 7.67647 23.8388 8.50465 22.8389 8.50485C21.8392 8.50505 21.0284 7.67707 21.0283 6.6556C21.0281 5.63412 21.8385 4.80594 22.8383 4.80574ZM7.44366 6.08007C7.75242 5.10845 8.77372 4.57672 9.72467 4.89215C10.6756 5.20757 11.1963 6.25095 10.8875 7.22246C10.5786 8.19397 9.5574 8.72581 8.60635 8.41038C7.65539 8.09486 7.13481 7.05158 7.44366 6.08007ZM15.9991 1.99854C16.5706 1.99843 17.0338 2.47152 17.0339 3.05524C17.034 3.63895 16.5709 4.11224 15.9995 4.11234C15.4282 4.11244 14.965 3.63936 14.9649 3.05554C14.9648 2.47193 15.4279 1.99864 15.9991 1.99854Z",fill:l}))},Q4=function(c){var l=c.color;return X.createElement("svg",{className:"icon thumbUp",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M11.4951 2.15479C13.058 2.1549 13.9932 3.10181 13.9932 4.68994L13.9902 4.7876L13.9854 4.91943C13.9854 6.17709 13.9823 7.70644 13.9795 8.53271L16.0498 8.59424H19.4609C20.6654 8.65866 21.8447 9.41248 21.8447 10.9419C21.8419 11.6112 21.671 12.0956 21.4609 12.4429C21.6738 12.7762 21.8418 13.2304 21.8418 13.8354C21.8417 14.4908 21.5792 15.099 21.0947 15.5835C21.2207 15.8775 21.3154 16.2616 21.3154 16.7515C21.3153 17.5384 20.9036 18.219 20.167 18.6812C20.237 19.0285 20.2319 19.4209 20.1562 19.8774C20.1506 19.9138 20.139 19.9533 20.125 19.9868C19.562 21.5131 18.6627 21.8432 15.0498 21.8433C12.7921 21.8433 11.8338 21.5633 11.0635 21.3364L10.8564 21.2778C10.5399 21.1854 10.2847 21.1128 10.125 21.1128L7.32129 21.0757C7.27501 21.0757 7.23121 21.0643 7.1875 21.0522C7.16787 21.0469 7.14785 21.041 7.12793 21.0366C6.86755 21.255 6.53994 21.3755 6.20117 21.3784H3.60938C2.80571 21.3783 2.15645 20.726 2.15625 19.9224V12.3257C2.15625 11.5219 2.80839 10.8688 3.60938 10.8687H6.20117C6.53432 10.8688 6.83955 10.9869 7.08594 11.1772C7.63489 10.8972 8.85279 10.135 8.9707 8.90283C9.07993 7.77973 9.06622 6.37614 9.04102 5.26416L9.03809 5.22217C9.03547 5.1253 9.02834 5.05242 9.02051 4.97705C9.01998 4.97196 9.01909 4.96656 9.01855 4.96143L8.99902 4.73193C8.9318 3.78235 9.27327 3.20165 9.56738 2.88232C10.01 2.4062 10.6773 2.15479 11.4951 2.15479ZM11.4951 3.38428C11.1674 3.38428 10.7245 3.44014 10.4668 3.72021C10.2763 3.9247 10.1946 4.23618 10.2227 4.64795L10.2402 4.83838L10.2412 4.85303C10.2491 4.93173 10.2574 5.00898 10.2627 5.10107C10.2655 5.12346 10.2676 5.14607 10.2676 5.16846C10.2928 6.32531 10.3102 7.80994 10.1953 9.02002C10.0216 10.8407 8.4134 11.8797 7.6543 12.269V12.2778C7.65431 12.2861 7.65585 12.2937 7.65723 12.3013C7.65863 12.309 7.66016 12.3173 7.66016 12.3257V19.8462L10.1367 19.8804C10.4643 19.8805 10.7895 19.9727 11.1982 20.0903L11.4141 20.1519L11.4248 20.1548C12.1532 20.3669 12.9821 20.6079 15.0527 20.6079C18.492 20.6079 18.7076 20.2747 18.9541 19.6108C19.0381 19.0648 18.9571 18.8207 18.873 18.6694C18.7109 18.3699 18.8226 17.998 19.1191 17.8354C19.1387 17.8242 19.1562 17.8155 19.1758 17.8071C19.5903 17.6362 20.0859 17.3085 20.0859 16.7456C20.0859 16.3506 19.9963 16.0393 19.8311 15.8433C19.7191 15.7117 19.6661 15.5355 19.6885 15.3647C19.7109 15.194 19.8055 15.0369 19.9482 14.936C20.3907 14.6392 20.6151 14.2667 20.6152 13.8354C20.6152 13.4349 20.4919 13.1211 20.251 12.897C20.0017 12.6645 19.9882 12.2722 20.2207 12.0229L20.2539 11.9888C20.4862 11.7759 20.6123 11.4121 20.6123 10.939C20.6123 9.98939 19.7804 9.84041 19.4219 9.8208H16.0303L13.3467 9.74268C13.0133 9.73427 12.7472 9.45967 12.75 9.12354C12.7501 9.10264 12.7578 6.7183 12.7578 4.90283V4.86865L12.7637 4.74854C12.7665 3.77385 12.3857 3.38436 11.4951 3.38428ZM3.60938 12.0952C3.48346 12.0954 3.38281 12.1997 3.38281 12.3257V19.9194C3.38295 20.0453 3.48633 20.1458 3.60938 20.146H6.20117C6.32141 20.1458 6.41921 20.0505 6.4248 19.9302V12.311C6.41906 12.1909 6.3213 12.0955 6.20117 12.0952H3.60938ZM4.88379 12.9448C5.33197 12.9448 5.64648 13.2584 5.64648 13.7065C5.64637 14.1546 5.33189 14.4683 4.88379 14.4683C4.4359 14.4681 4.12218 14.1545 4.12207 13.7065C4.12207 13.2557 4.43302 12.945 4.88379 12.9448Z",fill:l}))},Z4=function(c){var l=c.color;return X.createElement("svg",{className:"icon thumbUpFilled",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M11.4912 2.1543C13.0569 2.15436 13.9892 3.10415 13.9893 4.68945L13.9873 4.78711L13.9814 4.91895C13.9814 6.17934 13.9784 7.70592 13.9756 8.53223L16.0459 8.59375H19.457C20.6615 8.66098 21.8408 9.41208 21.8408 10.9414C21.8408 11.6135 21.6728 12.1008 21.46 12.4453C21.6728 12.7786 21.8409 13.2327 21.8438 13.832C21.8437 14.4902 21.583 15.0955 21.0957 15.5801C21.2217 15.8742 21.3174 16.258 21.3174 16.748C21.3173 17.535 20.9055 18.2156 20.1689 18.6777C20.239 19.025 20.2328 19.4175 20.1572 19.874C20.1488 19.9103 20.1409 19.9471 20.127 19.9834C19.5639 21.5098 18.6618 21.8398 15.0518 21.8398C12.8115 21.8398 11.8508 21.5613 11.083 21.3379L11.0684 21.333L10.8584 21.2744C10.5419 21.1792 10.2894 21.1094 10.127 21.1094L7.32031 21.0732C7.27438 21.0732 7.23201 21.0608 7.18945 21.0488C7.16986 21.0433 7.15015 21.0376 7.12988 21.0332C6.87778 21.2433 6.55509 21.375 6.20215 21.375H3.61133C2.80756 21.3749 2.15541 20.7227 2.15527 19.9189V12.3252C2.15534 11.5186 2.80752 10.8653 3.61133 10.8652H6.19727C6.53044 10.8653 6.83563 10.9834 7.08203 11.1738C7.63102 10.8909 8.84895 10.1317 8.9668 8.89941C9.07602 7.77629 9.06231 6.3727 9.03711 5.26074C9.03711 5.25377 9.03683 5.24622 9.03613 5.23926C9.03545 5.23245 9.0342 5.22556 9.03418 5.21875C9.03156 5.12464 9.02442 5.05182 9.0166 4.97656C9.01606 4.9714 9.01519 4.96614 9.01465 4.96094L8.99512 4.73145C8.92793 3.78194 9.26937 3.20115 9.56348 2.88184C10.0061 2.40572 10.6733 2.1543 11.4912 2.1543ZM4.88281 12.9463C4.43488 12.9465 4.12115 13.261 4.12109 13.709C4.12126 14.1568 4.43496 14.4705 4.88281 14.4707C5.33087 14.4707 5.64241 14.157 5.64258 13.709C5.64252 13.2581 5.33095 12.9463 4.88281 12.9463Z",fill:l}))},X4=function(c){var l=c.color;return X.createElement("svg",{className:"icon thumbDown",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M15.0479 2.1543C18.6613 2.1543 19.56 2.48499 20.123 4.00879C20.1371 4.0424 20.1487 4.08175 20.1543 4.11816C20.2299 4.57446 20.235 4.9663 20.165 5.31348C20.9017 5.77566 21.3135 6.45703 21.3135 7.24414C21.3134 7.73424 21.2188 8.11803 21.0928 8.41211C21.5773 8.89669 21.8408 9.50474 21.8408 10.1602C21.8408 10.7648 21.6727 11.2185 21.46 11.5518C21.67 11.8991 21.8409 12.3842 21.8438 13.0537C21.8436 14.5828 20.6642 15.3358 19.46 15.4004H16.0479L13.9775 15.4629C13.9803 16.2893 13.9834 17.8187 13.9834 19.0762L13.9893 19.207L13.9922 19.3057C13.9921 20.8938 13.0562 21.8408 11.4932 21.8408C10.6754 21.8408 10.009 21.5884 9.56641 21.1123C9.2723 20.793 8.92989 20.2132 8.99707 19.2637L9.0166 19.0342L9.01855 19.0186C9.0264 18.9431 9.03449 18.8704 9.03711 18.7734L9.03906 18.7314C9.06427 17.6194 9.07896 16.216 8.96973 15.0928C8.85208 13.8603 7.633 13.0985 7.08398 12.8184C6.83756 13.0087 6.53239 13.126 6.19922 13.126H3.6084C2.80735 13.126 2.15442 12.4737 2.1543 11.6699V4.07617C2.1543 3.27225 2.80728 2.61914 3.6084 2.61914H6.19922C6.53802 2.62196 6.86553 2.74259 7.12598 2.96094C7.14588 2.95661 7.16593 2.95168 7.18555 2.94629C7.22934 2.93425 7.27298 2.92191 7.31934 2.92188L10.123 2.88574C10.2827 2.88574 10.538 2.81216 10.8545 2.71973L11.0615 2.66113C11.8318 2.43424 12.7902 2.1543 15.0479 2.1543ZM15.0479 3.38672C12.9771 3.38672 12.1483 3.62773 11.4199 3.83984L11.4092 3.84375L11.1934 3.90527C10.7845 4.02289 10.4595 4.11523 10.1318 4.11523L7.65527 4.14844V11.6699C7.65525 11.6782 7.65469 11.6858 7.65332 11.6934C7.65192 11.7011 7.65039 11.7094 7.65039 11.7178V11.7256C8.40951 12.115 10.0166 13.1543 10.1904 14.9746C10.3053 16.1846 10.2889 17.6694 10.2637 18.8262C10.2637 18.8485 10.2606 18.8712 10.2578 18.8936C10.2525 18.9858 10.2452 19.0638 10.2373 19.1426L10.2354 19.1572L10.2188 19.3477C10.1908 19.7594 10.2724 20.0699 10.4629 20.2744C10.7206 20.5544 11.1625 20.6113 11.4902 20.6113C12.3809 20.6113 12.7625 20.2216 12.7598 19.2471L12.7539 19.126C12.7483 17.7087 12.7451 16.2884 12.7451 14.8711C12.7425 14.5351 13.0086 14.2604 13.3418 14.252L16.0283 14.1738H19.4199C19.7784 14.1542 20.6111 14.0059 20.6113 13.0566C20.6113 12.5833 20.4844 12.2187 20.252 12.0059L20.2188 11.9727C19.9863 11.7234 20.0007 11.3311 20.25 11.0986C20.4908 10.8746 20.6139 10.5606 20.6084 10.1631C20.6084 9.73451 20.3841 9.36111 19.9443 9.05859C19.8017 8.95782 19.7061 8.80154 19.6836 8.63086C19.6612 8.46004 19.7142 8.28301 19.8262 8.15137C19.9914 7.95537 20.081 7.64474 20.0811 7.25C20.0811 6.68697 19.5855 6.35837 19.1709 6.1875C19.1515 6.17918 19.1346 6.17123 19.1152 6.16016C18.8184 5.99773 18.7059 5.62488 18.8682 5.3252C18.9522 5.17393 19.0342 4.93001 18.9502 4.38379C18.7037 3.71996 18.4875 3.38672 15.0479 3.38672ZM3.6084 3.85156C3.48515 3.85156 3.38086 3.95305 3.38086 4.0791V11.6699C3.38098 11.7959 3.48242 11.8994 3.6084 11.8994H6.19922C6.31962 11.8994 6.41725 11.804 6.42285 11.6836V4.06445C6.41434 3.94694 6.31954 3.84869 6.19922 3.84863L3.6084 3.85156ZM4.88281 9.52637C5.33087 9.52647 5.64453 9.84096 5.64453 10.2891C5.64439 10.737 5.33077 11.0507 4.88281 11.0508C4.43194 11.0508 4.12124 10.7399 4.12109 10.2891C4.12109 9.84088 4.43463 9.52637 4.88281 9.52637Z",fill:l}),"  	")},$4=function(c){var l=c.color;return X.createElement("svg",{className:"icon thumbDownFilled",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M15.0518 2.1543C18.6617 2.15433 19.5649 2.48449 20.1279 4.01074C20.1419 4.04701 20.1498 4.08385 20.1582 4.12012C20.2338 4.57655 20.2399 4.96915 20.1699 5.31641C20.9062 5.77849 21.3182 6.45932 21.3184 7.24609C21.3184 7.73613 21.2227 8.12096 21.0967 8.41504C21.5838 8.89947 21.8446 9.50416 21.8447 10.1621C21.8419 10.7642 21.6737 11.2185 21.4609 11.5518C21.6737 11.8962 21.8417 12.3837 21.8418 13.0557C21.8418 14.5849 20.6623 15.3359 19.458 15.4033H16.0469L13.9766 15.4648C13.9794 16.2911 13.9824 17.8177 13.9824 19.0781L13.9873 19.21L13.9902 19.3086C13.99 20.8935 13.0574 21.8425 11.4922 21.8428C10.6744 21.8428 10.007 21.5913 9.56445 21.1152C9.27034 20.7959 8.92887 20.2152 8.99609 19.2656L9.01562 19.0361L9.01758 19.0205C9.0254 18.9453 9.03254 18.8724 9.03516 18.7783C9.0352 18.7715 9.03643 18.7646 9.03711 18.7578C9.0378 18.7509 9.03809 18.7433 9.03809 18.7363C9.06329 17.6244 9.077 16.2207 8.96777 15.0977C8.84988 13.8657 7.63212 13.1063 7.08301 12.8232C6.83674 13.0135 6.53121 13.1316 6.19824 13.1318H3.6123C2.8084 13.1318 2.15527 12.4786 2.15527 11.6719V4.0752C2.15557 3.27154 2.80858 2.61916 3.6123 2.61914H6.20312C6.55588 2.61926 6.87887 2.75095 7.13086 2.96094C7.15096 2.95653 7.17098 2.95076 7.19043 2.94531C7.23283 2.93344 7.27553 2.92188 7.32129 2.92188L10.1279 2.88477C10.2904 2.88457 10.5425 2.81478 10.8584 2.71973L11.0693 2.66113L11.084 2.65723C11.8517 2.43388 12.8121 2.15433 15.0518 2.1543ZM4.88379 9.52637C4.43593 9.52649 4.12232 9.84028 4.12207 10.2881C4.12207 10.7362 4.43575 11.0507 4.88379 11.0508C5.33171 11.0506 5.64355 10.7389 5.64355 10.2881C5.6433 9.84036 5.33153 9.5266 4.88379 9.52637Z",fill:l}))},K4=function(c){var l=c.color;return X.createElement("svg",{className:"icon addMore",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M11.9962 3.0304C12.5 3.0304 12.8007 3.43247 12.8009 3.82727V11.1222H20.1691C20.6171 11.1478 20.9733 11.5033 20.9992 11.9513C21.0248 12.4327 20.6559 12.8483 20.172 12.8741H12.8751V20.171C12.8519 20.6163 12.4933 20.9749 12.048 20.9982C11.5666 21.0238 11.1509 20.6549 11.1251 20.171V12.8058H3.82825C3.38295 12.7826 3.02436 12.4923 3.0011 12.047C2.9755 11.5656 3.34439 11.2154 3.82825 11.1896H11.1945V3.82727C11.1947 3.42856 11.5159 3.0304 11.9962 3.0304Z",fill:l}))},J4=function(c){var l=c.color;return X.createElement("svg",{className:"icon close",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M16.7666 6.21074C17.0486 5.9287 17.506 5.92879 17.7881 6.21074C18.0465 6.46922 18.0687 6.8749 17.8535 7.158L17.7881 7.23222L13.0215 11.9988L17.789 16.7654C18.071 17.0474 18.0708 17.5048 17.789 17.7869C17.5305 18.0455 17.1239 18.0678 16.8408 17.8523L16.7675 17.7869L12 13.0203L7.23337 17.7879C6.95141 18.0698 6.49399 18.0696 6.21188 17.7879C5.95331 17.5293 5.93098 17.1228 6.14645 16.8396L6.21188 16.7664L10.9785 11.9988L6.21188 7.23222C5.92987 6.95021 5.93 6.49283 6.21188 6.21074C6.47034 5.95229 6.87605 5.93015 7.15915 6.14531L7.23337 6.21074L12 10.9773L16.7666 6.21074Z",fill:l}))},F4=function(c){var l=c.color;return X.createElement("svg",{className:"icon back",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M13.3022 4.27266C13.7026 3.89234 14.3428 3.91239 14.7231 4.3127C15.1035 4.71306 15.0835 5.35326 14.6831 5.7336L8.02783 11.9992L14.6831 18.2648C15.0935 18.6452 15.1035 19.2854 14.7231 19.6857C14.3428 20.0861 13.7026 20.1061 13.3022 19.7258L5.86572 12.7297C5.66559 12.5395 5.54541 12.2694 5.54541 11.9992C5.54541 11.719 5.65558 11.4589 5.86572 11.2688L13.3022 4.27266Z",fill:l}))},W4=function(c){var l=c.color;return X.createElement("svg",{className:"icon next",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M9.27648 4.31223C9.65682 3.91188 10.298 3.89186 10.6984 4.2722L18.1349 11.2683C18.335 11.4584 18.4552 11.7286 18.4552 11.9988C18.4552 12.2789 18.345 12.5391 18.1349 12.7292L10.6984 19.7253C10.298 20.1057 9.65682 20.0856 9.27648 19.6853C8.89658 19.285 8.91654 18.6446 9.31652 18.2644L15.9728 11.9988L9.31652 5.73313C8.90655 5.35288 8.89657 4.71252 9.27648 4.31223Z",fill:l}))},P4=function(c){var l=c.color;return X.createElement("svg",{className:"icon star",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M12.0015 2.172C12.825 2.172 13.5649 2.64557 13.9263 3.41028L15.7886 7.34583C15.7886 7.34846 15.7888 7.34894 15.7915 7.35168C15.8 7.36836 15.817 7.3821 15.8364 7.38489L20.0015 8.01477C20.7969 8.13521 21.4558 8.69835 21.7163 9.48254C21.9852 10.2949 21.7858 11.1723 21.1919 11.7745L18.1782 14.8361C18.1559 14.8585 18.1473 14.8894 18.1529 14.923L18.8648 19.2501C19.0048 20.1072 18.6599 20.9475 17.9654 21.4406C17.6012 21.6982 17.1782 21.8273 16.7525 21.8273C16.4079 21.8273 16.0631 21.7402 15.7466 21.5665L12.0181 19.5245C12.007 19.5192 11.993 19.519 11.982 19.5245L8.25344 21.5665C7.54762 21.953 6.6968 21.9053 6.0386 21.4376C5.34112 20.9446 4.99633 20.1043 5.13918 19.2472L5.85012 14.923C5.85569 14.8895 5.84513 14.8584 5.82571 14.8361L2.81106 11.7745C2.21722 11.1723 2.01884 10.2949 2.29055 9.48254C2.55113 8.69848 3.20909 8.13816 4.00442 8.0177L8.16946 7.38782C8.18906 7.38502 8.20597 7.37044 8.21438 7.35364V7.35168C8.21438 7.35168 8.21582 7.35028 8.21731 7.34875L10.0806 3.41321C10.4391 2.64578 11.1781 2.1721 12.0015 2.172ZM12.0015 3.26184C11.5954 3.26194 11.245 3.49158 11.063 3.87805L9.20071 7.81067C9.03824 8.15801 8.7185 8.39846 8.34035 8.46008H8.33547L4.1675 9.09094C3.77262 9.14975 3.45545 9.42391 3.32375 9.82434C3.21451 10.1549 3.22621 10.6401 3.59035 11.0099L6.60403 14.0743L6.60696 14.0763C6.87019 14.3451 6.98781 14.7292 6.92629 15.1017L6.21438 19.4269C6.12757 19.9563 6.39118 20.3568 6.66848 20.5529C6.82255 20.6621 7.23435 20.8861 7.73293 20.6144L11.4614 18.5724C11.6294 18.4801 11.814 18.4328 12.0015 18.4327C12.1892 18.4327 12.3746 18.4799 12.5454 18.5724L16.271 20.6144C16.7667 20.8859 17.1814 20.6621 17.3355 20.5529C17.6127 20.3567 17.8754 19.9589 17.7886 19.4269L17.0777 15.1017C17.0161 14.732 17.1337 14.3481 17.397 14.0792L17.3999 14.0763L20.4165 11.0118C20.7803 10.6421 20.7914 10.1577 20.6822 9.82727C20.5477 9.42689 20.2342 9.15273 19.8394 9.09387L15.6714 8.46008H15.6655C15.2874 8.39843 14.9648 8.15797 14.8052 7.81067L12.9429 3.87805C12.7608 3.4915 12.4076 3.26184 12.0015 3.26184Z",fill:l}))},I4=function(c){var l=c.color;return X.createElement("svg",{className:"icon starFilled",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M11.9949 2.172C12.8212 2.172 13.5584 2.64837 13.9197 3.41028L15.6502 7.06555C15.7902 7.32876 15.8354 7.38793 16.1404 7.43274L19.9978 8.0177C20.7933 8.13814 21.4494 8.7012 21.7127 9.48547C21.9844 10.2866 21.7822 11.1752 21.1912 11.7775L18.4793 14.5079C18.1685 14.8187 18.1574 14.8475 18.2078 15.2228L18.8582 19.2531C18.9982 20.1101 18.6534 20.9505 17.9588 21.4435C17.6059 21.6955 17.1829 21.8331 16.7488 21.8331C16.3959 21.8331 16.0511 21.7433 15.743 21.5724L12.1824 19.6202C12.0453 19.5559 11.9309 19.5395 11.7937 19.629L8.24686 21.5724C7.54389 21.9588 6.68458 21.9111 6.03202 21.4435C5.33454 20.9505 4.99255 20.1102 5.1326 19.2531L5.79667 15.2247C5.86092 14.9087 5.80734 14.8327 5.61698 14.6339L2.80448 11.7775C2.21064 11.1724 2.00946 10.2866 2.28397 9.48547C2.54454 8.70135 3.20246 8.13815 3.99784 8.0177L7.89432 7.42688C8.13239 7.38206 8.2029 7.34853 8.35135 7.04602L10.074 3.41028C10.4325 2.64565 11.1687 2.17208 11.9949 2.172Z",fill:l}))},e3=function(c){var l=c.color;return X.createElement("svg",{className:"icon checkCircle",viewBox:"0 0 24 24",fill:l},X.createElement("path",{d:"M11.9971 0.999023C15.4921 0.999059 18.2098 1.91105 20.0781 3.70996C22.0186 5.57803 23 8.36827 23 12.001C22.9999 15.6335 22.0154 18.4201 20.0781 20.2881C18.2098 22.087 15.4921 22.999 11.9971 22.999C8.502 22.999 5.78414 22.087 3.91895 20.2881C1.98167 18.4232 1.00003 15.6365 1 12.0039C1 8.37119 1.98164 5.58096 3.91895 3.71289C5.78414 1.91408 8.5052 0.999023 11.9971 0.999023ZM11.9971 2.21875C5.50804 2.21875 2.21582 5.50953 2.21582 12.0039C2.21588 18.4919 5.5081 21.7832 11.9971 21.7832C18.489 21.7831 21.7841 18.4918 21.7842 12.0039C21.7842 5.51274 18.4891 2.21882 11.9971 2.21875ZM16.3916 7.2959C16.5828 7.04211 16.9434 6.98874 17.1973 7.17969C17.4512 7.37086 17.5046 7.73147 17.3135 7.98535L10.8623 16.624C10.7558 16.768 10.5896 16.8522 10.4111 16.8555H10.3984C10.2229 16.8555 10.056 16.7776 9.94629 16.6396L6.99414 12.9287C6.79665 12.6811 6.83732 12.3176 7.08496 12.1201C7.33261 11.9227 7.69606 11.9633 7.89355 12.2109L10.3828 15.3389L16.3916 7.2959Z",fill:l}))},Pt=function(c){var l=c.onClick,u=c.state,r=c.backgroundColor,o=c.iconColor,d=c.ariaLabel,m=c.className,g=c.children,b=c.iconType,p=c.size,S=c.id,R=C.useState(!1),H=R[0],Z=R[1],j=function(){u!=="disabled"&&(Z(!0),l(),setTimeout(function(){return Z(!1)},150))},V=u==="disabled",k=g!=null?g:b?X.createElement(D4,{type:b,color:o!=null?o:""}):null;return X.createElement("button",{type:"button",className:"".concat(m!=null?m:""," icon-button ").concat(H?"clicked":""," ").concat(V?"disabled":""," ").concat(p),onClick:j,"aria-label":d,style:{backgroundColor:r},disabled:V,id:S},k)},V2=function(c){var l=c.onClick,u=c.isDisabled,r=u===void 0?!1:u,o=c.isLoading,d=o===void 0?!1:o,m=c.text,g=c.leftIcon,b=c.backgroundColor,p=c.textColor,S=c.borderRadius,R=c.className,H=c.ariaLabel,Z=c.id,j=C.useState(!1),V=j[0],k=j[1],ee=function(){!r&&!d&&(k(!0),l(),setTimeout(function(){return k(!1)},200))};return X.createElement("button",{type:"button",onClick:ee,className:"".concat(R!=null?R:""," button-primary label1 ").concat(r?"disabled":""," ").concat(d?"loading":""," ").concat(V?" clicked":""),disabled:r||d,style:{backgroundColor:b,color:p,borderRadius:S,position:"relative"},"aria-label":H,id:Z},X.createElement("span",{className:"button-content",style:{opacity:d?0:1}},g,m),d&&X.createElement("div",{className:"button-spinner-wrapper"},X.createElement(_r,{color:p,size:"small"})))},t3=function(c){var l=c.text,u=c.interval,r=u===void 0?3e3:u,o=c.spinnerColor,d=c.textColor,m=c.className,g=c.id,b=C.useState(0),p=b[0],S=b[1],R=Array.isArray(l),H=R?l:[l],Z=H.length>0&&!!H[0];return C.useEffect(function(){if(!(H.length<=1)){var j=setInterval(function(){S(function(V){return(V+1)%H.length})},r);return function(){return clearInterval(j)}}},[H,r]),X.createElement("div",{className:"".concat(m!=null?m:""," loadBlock"),role:"status","aria-live":"polite",id:g},X.createElement("span",{className:"text title3 bold",style:{color:d}},Z?H[p]:"Cargando..."),X.createElement(_r,{color:o}))},a3=function(c){var l=c.onCancel,u=c.onConfirm,r=c.onClose,o=c.isConfirmDisabled,d=o===void 0?!1:o,m=c.title,g=c.body,b=c.contentChildren,p=c.id,S=c.cancelText,R=S===void 0?"Cancelar":S,H=c.confirmText,Z=H===void 0?"Aceptar":H,j=c.className;return X.createElement("div",{className:"".concat(j!=null?j:""," modal-fullscreen-wrapper"),id:p,role:"dialog","aria-modal":"true"},X.createElement("div",{className:"modal-overlay",onClick:l}),X.createElement("div",{className:"modal-wrapper ".concat(r?"top-padding":"")},r&&X.createElement(Pt,{onClick:r,size:"big",state:"default",iconType:"close",backgroundColor:"transparent",ariaLabel:"Cerrar modal",className:"button-close"}),X.createElement("div",{className:"modal-content"},X.createElement("div",{className:"modal-header"},X.createElement("h2",{className:"modal-title title2 bold"},m),g&&X.createElement("p",{className:"modal-body body1"},g)),b),X.createElement("div",{className:"modal-footer"},l&&X.createElement(L4,{onClick:l,text:R}),u&&X.createElement(V2,{isDisabled:d,onClick:u,text:Z}))))};const n3=({onGameReady:c})=>{const[l,u]=C.useState(!1),[r,o]=C.useState(!1),{initializeApp:d,isSilentMode:m}=B2(),g=()=>q(null,null,function*(){u(!0),o(!1);try{console.log("WelcomeScreen: 🚀 Iniciando inicialización completa..."),yield d(),console.log("✅ WelcomeScreen: Inicialización completada"),m&&(console.warn("⚠️ WelcomeScreen: Modo silencioso activado - sin voz"),o(!0)),c()}catch(b){console.error("❌ WelcomeScreen: Error en inicialización:",b),o(!0),c()}finally{u(!1)}});return A.jsx("div",{id:"welcome-screen-overlay",children:A.jsxs("div",{className:"welcome-screen",children:[A.jsx("h1",{children:"El velo del misterio se alza."}),A.jsx("p",{children:"¿Estás listo para enfrentarte a Enygma y desvelar el personaje oculto en el que está pensando?"}),A.jsx("div",{className:"text-center",children:A.jsx(V2,{onClick:g,isDisabled:l,isLoading:l,text:l?"Preparando...":"Empezar",backgroundColor:"#88FFD5",textColor:"#001428",borderRadius:"8px"})}),l&&A.jsx("p",{className:"disclaimer-text",children:"Configurando la experiencia mágica..."}),r&&A.jsx("div",{className:"silent-mode-warning",style:{marginTop:"20px",padding:"10px",backgroundColor:"rgba(255, 200, 0, 0.2)",borderRadius:"8px",fontSize:"14px"},children:A.jsxs("p",{style:{margin:0},children:[A.jsx("span",{role:"img","aria-label":"warning",children:"⚠️"})," Modo silencioso activado: La aplicación funcionará sin voz."]})})]})})},l3=()=>A.jsx(Pt,{ariaLabel:"Back",iconType:"back",onClick:()=>{},size:"small",state:"default",backgroundColor:"transparent",iconColor:"#FFFFFF"}),i3=()=>A.jsx("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:A.jsx("path",{d:"M14.4547 3.45897C15.254 2.67839 16.7588 2.67839 17.5543 3.45897L28.5641 14.2783C29.1206 14.8236 29.2776 15.5934 28.9713 16.2881C28.6537 17.0235 27.8804 17.498 27.0104 17.498H26.0397V27.208C26.0397 28.2649 25.0946 29.124 23.9293 29.124H20.0114C19.7016 29.1275 19.4477 28.8813 19.444 28.5752V23.1816H12.6276V28.5752C12.6238 28.8814 12.3664 29.1276 12.0602 29.1201H7.91468C6.74944 29.1201 5.80433 28.261 5.80433 27.2041V17.4941H4.99086C4.12079 17.4941 3.35121 17.0197 3.02992 16.2842C2.73117 15.5933 2.88766 14.8237 3.44398 14.2822L14.4547 3.45897Z",fill:"white"})}),u2=()=>A.jsx(Pt,{ariaLabel:"Menu",iconType:"menu",onClick:()=>{},size:"small",state:"default",backgroundColor:"transparent",iconColor:"#FFFFFF"}),s3=()=>A.jsx(Pt,{ariaLabel:"Music On",iconType:"musicOn",onClick:()=>{},size:"small",state:"default",backgroundColor:"transparent",iconColor:"#FFFFFF"}),c3=()=>A.jsx(Pt,{ariaLabel:"Music Off",iconType:"musicOff",onClick:()=>{},size:"small",state:"default",backgroundColor:"transparent",iconColor:"#FFFFFF"}),u3=()=>A.jsx(Pt,{ariaLabel:"Sound On",iconType:"soundOn",onClick:()=>{},size:"small",state:"default",backgroundColor:"transparent",iconColor:"#FFFFFF"}),r3=()=>A.jsx(Pt,{ariaLabel:"Sound Off",iconType:"soundOff",onClick:()=>{},size:"small",state:"default",backgroundColor:"transparent",iconColor:"#FFFFFF"}),o3=({currentView:c,onBackToMain:l,onGoHome:u,showBackButton:r=!1})=>{var P,Q;const{state:{audioState:o,isMusicPlaying:d,isSpeechPlaying:m},toggleMute:g,pauseMusic:b,resumeMusic:p,pauseSpeech:S,resumeSpeech:R}=Vs(),[H,Z]=C.useState(!1),j=()=>{switch(c){case"play":return"Enygma";case"rules":return"Reglas";case"lives":return"Tus preguntas restantes";case"clues":return"Pistas descubiertas";default:return""}},V=()=>{d?b():p()},k=()=>{if(m)S();else{const w=window.audioManager;w&&w.hasSpeechAudio()?R():console.warn("No hay audio de narración disponible para reproducir")}},ee=()=>{Z(!0)},ue=((Q=(P=window.audioManager)==null?void 0:P.hasSpeechAudio)==null?void 0:Q.call(P))||!1;return A.jsxs(A.Fragment,{children:[A.jsxs("div",{className:"header",children:[A.jsxs("div",{className:"header-left",children:[c==="main"&&A.jsx(u2,{}),r&&c!=="main"&&l&&A.jsx("div",{className:"back-button",onClick:l,children:A.jsx(l3,{})})]}),A.jsx("div",{className:"header-title",children:j()}),A.jsxs("div",{className:"header-right",children:[A.jsx("div",{className:`sound-icon music-control ${o.isMuted?"muted":""} ${d?"active":""}`,onClick:V,title:o.isMuted?"Música silenciada":d?"Pausar música":"Reanudar música",children:!d||o.isMuted?A.jsx(c3,{}):A.jsx(s3,{})}),A.jsx("div",{className:`sound-icon speech-control ${o.isMuted?"muted":""} ${m?"active":""} ${!ue&&!m?"no-audio":""}`,onClick:k,title:o.isMuted?"Narración silenciada":m?"Pausar narración":ue?"Reanudar narración":"No hay narración disponible",children:!m||o.isMuted?A.jsx(r3,{}):A.jsx(u3,{})}),A.jsx("div",{className:"sound-icon menu-control",onClick:ee,title:"Configuración avanzada de audio",children:A.jsx(u2,{})}),A.jsx("div",{className:"home-icon",onClick:u,children:A.jsx(i3,{})})]})]}),H&&A.jsx("div",{className:"audio-menu-overlay",onClick:()=>Z(!1),children:A.jsxs("div",{className:"audio-menu",onClick:w=>w.stopPropagation(),children:[A.jsx("h3",{children:"Control de Audio"}),A.jsxs("div",{className:"audio-actions",children:[A.jsx("button",{onClick:()=>{g(),Z(!1)},className:o.isMuted?"unmute":"mute",children:o.isMuted?"🔊 Activar Audio":"🔇 Silenciar Todo"}),A.jsx("button",{onClick:()=>Z(!1),children:"Cerrar"})]})]})})]})},f3=({handleStartGame:c,handleShowRules:l,isStartingGame:u,isReady:r})=>{const[o,d]=C.useState([]);return C.useEffect(()=>{q(null,null,function*(){try{const b=yield(yield fetch("/game-modes.json")).json();d(b.gameModes||[])}catch(g){}})},[]),A.jsxs("div",{className:"content",children:[A.jsx("div",{className:"menu-left",children:A.jsxs("div",{onClick:l,style:{cursor:"pointer"},children:[A.jsx("img",{src:"assets/game/book.png",alt:"Book",className:"book-image"}),A.jsx("div",{children:"Reglas"})]})}),A.jsxs("div",{className:"game",children:[A.jsxs("div",{className:"game-header",children:[A.jsx("h1",{children:"Enygma"}),A.jsx("p",{children:"¿Puedes adivinar el personaje que está pensando Enygma?"})]}),A.jsx("div",{className:"game-mode",children:o.filter(m=>m.enabled).map(m=>A.jsxs("div",{className:"enygma-wrapper",children:[A.jsx("img",{src:m.image,alt:"Enygma",className:"enygma-image"}),A.jsx("p",{children:m.description}),A.jsx("button",{onClick:()=>c(m.mode),disabled:u||!r,children:u?"Iniciando...":r?m.buttonText:"Preparando..."})]},m.id))})]})]})},d3=()=>A.jsx(Pt,{ariaLabel:"Aura",iconType:"aura",onClick:()=>{},size:"small",state:"default",backgroundColor:"transparent",iconColor:"#000000"}),h3=({isOpen:c,onConfirm:l,onCancel:u})=>(console.log("ExitGamePopup rendered, isOpen:",c),c?A.jsx(a3,{title:"¿Seguro que quieres salir del juego?",onClose:u,onCancel:l,onConfirm:u,cancelText:"Salir de todos modos",confirmText:"Seguir jugando",contentChildren:A.jsxs("div",{className:"popup-content",children:[A.jsx("p",{children:"Si sales ahora, vas a perder tu progreso actual."}),A.jsx("p",{children:"Puedes seguir jugando o salir cuando quieras."})]})}):null),m3=({handleShowLives:c,handleShowClues:l,handleExistGame:u,showExitPopup:r,handleConfirmExit:o,handleCancelExit:d,onGameEnd:m})=>{const{session:g,askQuestion:b,askInitialMessage:p}=jr(),{state:{isSpeechPlaying:S}}=Vs(),[R,H]=C.useState([]),[Z,j]=C.useState(""),[V,k]=C.useState(!1),ee=C.useRef(null),ue=()=>{var w;(w=ee.current)==null||w.scrollIntoView({behavior:"smooth"})};C.useEffect(()=>{ue()},[R]),C.useEffect(()=>{if(g!=null&&g.messages){const w=g.messages.map(G=>({id:G.id,text:G.text,sender:G.sender,timestamp:G.timestamp}));H(w)}},[g==null?void 0:g.messages]),C.useEffect(()=>{if(console.log("phase",g==null?void 0:g.phase),(g==null?void 0:g.phase)==="finished"){const w=setTimeout(()=>{m()},2e3);return()=>clearTimeout(w)}},[g==null?void 0:g.phase,m]),C.useEffect(()=>{g&&(!g.messages||g.messages.length===0)&&R.length===0&&q(null,null,function*(){try{k(!0),yield p("Hola")}catch(G){console.error("Error sending initial Hola:",G)}finally{k(!1)}})},[g,R.length,p]);const P=()=>q(null,null,function*(){if(!Z.trim()||V||!g)return;const w=Z.trim();j(""),k(!0);try{yield b(w)}catch(G){const O={id:`error-${Date.now()}`,text:"Lo siento, hubo un error al procesar tu mensaje. Inténtalo de nuevo.",sender:"ai",timestamp:new Date};H(J=>[...J,O])}finally{k(!1)}}),Q=w=>{w.key==="Enter"&&!w.shiftKey&&(w.preventDefault(),P())};return A.jsxs(A.Fragment,{children:[A.jsxs("div",{className:"content chat-view",children:[A.jsx("div",{className:"menu-left",children:A.jsxs("div",{className:"enygma-logo",children:[A.jsx("img",{src:"assets/game/enygma.png",alt:"Enygma",className:"enygma-image"}),A.jsx("div",{className:`${S?"speaking":""}`,children:S&&A.jsx("div",{className:"icon-aura speech-indicator",children:A.jsx("div",{className:"speech-pulse",children:A.jsx(d3,{})})})})]})}),A.jsxs("div",{className:"game chat-view-wrapper",children:[A.jsx("div",{className:"chat-container",children:A.jsxs("div",{className:"messages-list",children:[R.map(w=>A.jsx("div",{className:`message ${w.sender==="user"?"user-message":"ai-message"}`,children:A.jsxs("div",{className:"message-content",children:[A.jsx("span",{className:"message-text",children:w.text}),A.jsx("span",{className:"message-time",children:w.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})},w.id)),V&&A.jsx("div",{className:"message ai-message",children:A.jsx("div",{className:"message-content",children:A.jsx("span",{className:"message-text typing",children:"Enygma está pensando..."})})}),A.jsx("div",{ref:ee})]})}),A.jsx("div",{className:"chat-input-container",children:A.jsxs("div",{className:"input-wrapper",children:[A.jsx("input",{type:"text",value:Z,onChange:w=>j(w.target.value),onKeyDown:Q,placeholder:(g==null?void 0:g.mode)==="player_vs_ia"?"Responde con 'Sí', 'No', 'Tal vez' o 'No lo sé'...":"Haz una pregunta sobre el personaje...",disabled:V||!g,className:"chat-input"}),A.jsx("button",{onClick:P,disabled:!Z.trim()||V||!g,className:"send-button",children:V?"Enviando...":"Enviar"})]})})]}),A.jsxs("div",{className:"menu-right",children:[A.jsxs("div",{onClick:c,style:{cursor:"pointer"},children:[A.jsx("img",{src:"assets/game/lives.png",alt:"Vidas",className:"lives-image"}),g&&A.jsxs("div",{children:[g.questionCount,"/",g.maxQuestions]})]}),A.jsxs("div",{onClick:l,style:{cursor:"pointer"},children:[A.jsx("img",{src:"assets/game/clues.png",alt:"Pistas",className:"clues-image"}),A.jsx("div",{children:"Pistas"})]}),A.jsxs("div",{onClick:u,style:{cursor:"pointer"},children:[A.jsx("img",{src:"assets/game/exit.png",alt:"Salir",className:"exit-image"}),A.jsx("div",{children:"Salir"})]})]})]}),A.jsx(h3,{isOpen:r,onConfirm:o,onCancel:d})]})},p3=()=>A.jsx(Pt,{ariaLabel:"Back",iconType:"back",onClick:()=>{},size:"small",state:"default",backgroundColor:"transparent",iconColor:"#FFFFFF"}),g3=()=>A.jsx(Pt,{ariaLabel:"Next",iconType:"next",onClick:()=>{},size:"small",state:"default",backgroundColor:"transparent",iconColor:"#FFFFFF"}),y3=({isOpen:c,onClose:l})=>{const[u,r]=C.useState(null),[o,d]=C.useState(0),[m,g]=C.useState(!1),[b,p]=C.useState("next");C.useEffect(()=>(c?(q(null,null,function*(){try{const V=yield fetch("/game-rules.json");if(!V.ok)throw new Error(`HTTP error! status: ${V.status}`);const k=yield V.json();r(k)}catch(V){}}),d(0),document.body.style.overflow="hidden"):document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[c]),C.useEffect(()=>{const j=V=>{V.key==="Escape"&&c&&l()};return document.addEventListener("keydown",j),()=>document.removeEventListener("keydown",j)},[c,l]);const S=()=>{!u||o>=u.pages.length-1||m||(g(!0),p("next"),setTimeout(()=>{d(j=>j+1),g(!1)},300))},R=()=>{o<=0||m||(g(!0),p("prev"),setTimeout(()=>{d(j=>j-1),g(!1)},300))},H=j=>{j===o||m||(g(!0),p(j>o?"next":"prev"),setTimeout(()=>{d(j),g(!1)},300))};if(!c||!u)return null;const Z=u.pages[o];return A.jsxs("div",{className:"content rules-modal",children:[A.jsx("button",{className:`rules-nav-arrow prev ${o===0?"disabled":""}`,onClick:R,disabled:o===0||m,"aria-label":"Página anterior",children:A.jsx(p3,{})}),A.jsx("div",{className:`rules-page-container ${m?`page-flip-${b}`:""}`,onClick:j=>j.stopPropagation(),children:A.jsxs("div",{className:"rules-container",children:[A.jsx("div",{className:"rules-image-card",children:Z.image&&A.jsx("img",{src:Z.image,alt:Z.title,onError:j=>{j.currentTarget.style.display="none"}})}),A.jsxs("div",{className:"rules-text-card",children:[A.jsx("h3",{children:Z.title}),A.jsx("div",{className:"rules-page-indicators",children:u.pages.map((j,V)=>A.jsx("button",{className:`rules-page-indicator ${V===o?"active":""}`,onClick:()=>H(V),disabled:m,"aria-label":`Ir a página ${V+1}`},V))})]})]})}),A.jsx("button",{className:`rules-nav-arrow next ${o>=u.pages.length-1?"disabled":""}`,onClick:S,disabled:o>=u.pages.length-1||m,"aria-label":"Página siguiente",children:A.jsx(g3,{})})]})},v3=({})=>A.jsx("div",{className:"content lives-modal",children:A.jsxs("div",{className:"lives-wrapper",children:[A.jsx("div",{className:"game-header",children:A.jsx("h1",{children:"Tus preguntas restantes"})}),A.jsx("div",{className:"lives-container",children:A.jsx("div",{children:"Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada vez que haces una, se descuenta del contador. Piensa bien cada pregunta: ¡cada una cuenta!"})})]})}),b3=({})=>A.jsx("div",{className:"content clues-modal",children:A.jsxs("div",{className:"clues-wrapper",children:[A.jsx("div",{className:"game-header",children:A.jsx("h1",{children:"Pistas descubiertas"})}),A.jsx("div",{className:"clues-container",children:A.jsx("div",{children:"Aquí aparecerán las pistas que vayas descubriendo...Por ahora, los secretos permanecen ocultos en las sombras. Hazme tu primera pregunta y comenzaré a revelarlos."})})]})}),C3=({onPlayAgain:c})=>{const{session:l}=jr();if(!l||l.phase!=="finished")return null;const u=l.winner==="user",r=l.winner===void 0||l.winner==="draw",o=l.currentCharacter||l.finalGuess||"Personaje desconocido",d=()=>u?"¡Has acertado!":r?"¡Empate!":"¡No has acertado!",m=()=>u?`El personaje es ${o}`:`El personaje era ${o}`;return A.jsxs("div",{className:"content game-end-view",children:[A.jsx("div",{className:"menu-left",children:A.jsx("div",{className:"character-reveal",children:A.jsx("div",{className:"character-image-container",children:A.jsx("img",{src:"assets/game/character-placeholder.png",alt:o,className:"character-image",onError:g=>{g.target.src="assets/game/enygma.png"}})})})}),A.jsx("div",{className:"game game-end-content",children:A.jsxs("div",{className:"result-section",children:[A.jsx("h1",{className:`result-title ${u?"victory":r?"draw":"defeat"}`,children:d()}),A.jsx("p",{className:"character-reveal-text",children:m()}),A.jsx("div",{className:"action-buttons",children:A.jsx("button",{className:"play-again-button",onClick:c,children:"Volver a jugar"})})]})}),A.jsxs("div",{className:"menu-right promotional-content",children:[A.jsxs("div",{className:"promo-card",children:[A.jsx("div",{className:"promo-image",children:A.jsx("img",{src:"assets/promo/los-anos-nuevos.jpg",alt:"Los años nuevos",onError:g=>{g.target.src="assets/game/placeholder-promo.png"}})}),A.jsxs("div",{className:"promo-content",children:[A.jsx("h3",{children:"¿Te apetece ver contenido con Iria del Río?"}),A.jsx("p",{children:'Mira la serie "Los años nuevos"'}),A.jsx("button",{className:"promo-button",children:"Comenzar a ver"})]})]}),A.jsxs("div",{className:"promo-card",children:[A.jsx("div",{className:"promo-icon",children:A.jsx("img",{src:"assets/icons/perplexity-icon.png",alt:"Perplexity",onError:g=>{g.target.src="assets/game/placeholder-icon.png"}})}),A.jsxs("div",{className:"promo-content",children:[A.jsxs("h3",{children:["¿Quieres saber más sobre ",o,"?"]}),A.jsx("p",{children:"Pregunta a Perplexity"}),A.jsx("button",{className:"promo-button",children:"Abrir Perplexity"})]})]})]})]})};function S3(){const{isInitialized:c,errors:l}=s4(),{startGameFlow:u}=B2(),{stopAll:r}=k2(),[o,d]=C.useState("loading"),[m,g]=C.useState("main"),[b,p]=C.useState(null),[S,R]=C.useState(!1),[H,Z]=C.useState(!1),[j,V]=C.useState(!1);C.useEffect(()=>{if(c){const pe=localStorage.getItem("enygma_analytics_consent");d(pe?"welcome":"consent")}},[c]),C.useEffect(()=>{l.length>0&&console.warn("⚠️ App: Errores detectados:",l)},[l]);const k=C.useCallback(pe=>{p(m),g(pe)},[m]),ee=C.useCallback(()=>{b?(g(b),p(null)):g("main")},[b]),ue=C.useCallback(pe=>{p(null),g(pe)},[]),P=C.useCallback(()=>{d("welcome")},[]),Q=C.useCallback(()=>{d("welcome")},[]),w=C.useCallback(()=>{d("ready"),R(!0)},[]),G=pe=>q(null,null,function*(){Z(!0);try{S||R(!0),yield u(pe),k("play")}catch(v){console.error("Error al iniciar el juego:",v)}finally{Z(!1)}}),O=()=>{},J=()=>k("rules"),de=()=>k("lives"),Le=()=>k("clues"),xe=()=>k("gameEnd"),Se=()=>q(null,null,function*(){localStorage.removeItem("enygma_generated_character"),localStorage.removeItem("enygma_character_timestamp"),r(),Fe.stopSpeech(),ue("main")}),te=()=>{V(!0)},re=()=>{localStorage.removeItem("enygma_generated_character"),localStorage.removeItem("enygma_character_timestamp"),r(),Fe.stopSpeech(),ue("main"),V(!1),console.log("🧹 App: Juego finalizado, datos limpiados y audio detenido")},oe=()=>{V(!1)},L=()=>ee(),$=m!=="main"&&m!=="play"&&m!=="gameEnd";if(o==="loading")return A.jsx("div",{className:"loader-container",children:A.jsx("div",{className:"loader",children:A.jsx(t3,{interval:6e3,text:"Cargando..."})})});const le=()=>{switch(o){case"consent":return A.jsx(z4,{onAudioActivated:P,onConsentGiven:Q});case"welcome":return A.jsx(n3,{onGameReady:w})}switch(m){case"main":return A.jsx(f3,{handleStartGame:G,handleShowRules:J,isStartingGame:H,isReady:!0});case"play":return A.jsx(m3,{handleShowLives:de,handleShowClues:Le,handleExistGame:te,showExitPopup:j,handleConfirmExit:re,handleCancelExit:oe,onGameEnd:xe});case"rules":return A.jsx(y3,{isOpen:!0,onClose:L});case"lives":return A.jsx(v3,{});case"clues":return A.jsx(b3,{});case"gameEnd":return A.jsx(C3,{onPlayAgain:Se,onBackToMain:L});default:return A.jsx("div",{className:"content"})}};return A.jsx("div",{className:"App",children:A.jsxs("div",{className:"game-container",children:[A.jsx("img",{src:"assets/game/background.png",alt:"Background",className:"background"}),A.jsxs("div",{className:"board",children:[A.jsx(o3,{currentView:m,onBackToMain:L,onGoHome:O,showBackButton:$}),le()]})]})})}Nm.createRoot(document.getElementById("root")).render(A.jsx(c4,{children:A.jsx(r4,{children:A.jsx(p4,{children:A.jsx(y4,{children:A.jsx(M4,{children:A.jsx(N4,{children:A.jsx(x4,{children:A.jsx(S3,{})})})})})})})}))});export default E3();
