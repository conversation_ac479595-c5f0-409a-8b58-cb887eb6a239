// @TODO: cambiar por "IconButton" home

const IconHome: React.FC = () => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.4547 3.45897C15.254 2.67839 16.7588 2.67839 17.5543 3.45897L28.5641 14.2783C29.1206 14.8236 29.2776 15.5934 28.9713 16.2881C28.6537 17.0235 27.8804 17.498 27.0104 17.498H26.0397V27.208C26.0397 28.2649 25.0946 29.124 23.9293 29.124H20.0114C19.7016 29.1275 19.4477 28.8813 19.444 28.5752V23.1816H12.6276V28.5752C12.6238 28.8814 12.3664 29.1276 12.0602 29.1201H7.91468C6.74944 29.1201 5.80433 28.261 5.80433 27.2041V17.4941H4.99086C4.12079 17.4941 3.35121 17.0197 3.02992 16.2842C2.73117 15.5933 2.88766 14.8237 3.44398 14.2822L14.4547 3.45897Z"
        fill="white"
      />
    </svg>
  );
};

export default IconHome;
