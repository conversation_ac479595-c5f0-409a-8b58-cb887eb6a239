.modal-fullscreen-wrapper {
  position: absolute;

  .modal-wrapper {
    border: 2px solid #00d4aa;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 212, 170, 0.3);
    animation: popupSlideIn 0.3s ease-out;

    .modal-title {
      color: white;
    }

    .popup-content {
      color: #89969F;
      font-size: 18px;
    }

    .button-secondary {
      background: #88FFD5;
      color: black;
    }

    .button-primary {
      background-color: transparent;
      border: 1px solid #FFFFFF;
    }
  }
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
