import { Modal } from "microapps";
import "./ExitGamePopup.scss";

interface ExitGamePopupProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export const ExitGamePopup: React.FC<ExitGamePopupProps> = ({
  isOpen,
  onConfirm,
  onCancel,
}) => {
  console.log("ExitGamePopup rendered, isOpen:", isOpen);

  if (!isOpen) return null;

  return (
    <Modal
      title="¿Seguro que quieres salir del juego?"
      onClose={onCancel}
      onCancel={onConfirm}
      onConfirm={onCancel}
      cancelText="Salir de todos modos"
      confirmText="Seguir jugando"
      contentChildren={
        <div className="popup-content">
          <p>Si sales ahora, vas a perder tu progreso actual.</p>
          <p>Puedes seguir jugando o salir cuando quieras.</p>
        </div>
      }
    />
  );
};
